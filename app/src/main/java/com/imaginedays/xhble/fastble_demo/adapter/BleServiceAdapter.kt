package com.imaginedays.xhble.fastble_demo.adapter

import android.bluetooth.BluetoothGattCharacteristic
import android.bluetooth.BluetoothGattService
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.card.MaterialCardView
import com.imaginedays.xhble.R
import com.imaginedays.xhble.fastble_demo.constants.BleUuidConstants

class BleServiceAdapter(private val context: Context) : RecyclerView.Adapter<BleServiceAdapter.ServiceViewHolder>() {

    private val services = mutableListOf<BluetoothGattService>()
    private var onServiceClickListener: ((BluetoothGattService) -> Unit)? = null
    private var onCharacteristicClickListener: ((BluetoothGattService, BluetoothGattCharacteristic) -> Unit)? = null
    private val characteristicValues = mutableMapOf<String, ByteArray>()

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ServiceViewHolder {
        val view = LayoutInflater.from(context).inflate(R.layout.item_ble_service, parent, false)
        return ServiceViewHolder(view)
    }

    override fun onBindViewHolder(holder: ServiceViewHolder, position: Int) {
        holder.bind(services[position])
    }

    override fun getItemCount(): Int = services.size

    /**
     * 更新服务列表
     */
    fun updateServices(newServices: List<BluetoothGattService>) {
        services.clear()
        services.addAll(newServices)
        notifyDataSetChanged()
    }

    /**
     * 清空服务列表
     */
    fun clearServices() {
        services.clear()
        notifyDataSetChanged()
    }

    /**
     * 设置服务点击监听器
     */
    fun setOnServiceClickListener(listener: (BluetoothGattService) -> Unit) {
        onServiceClickListener = listener
    }

    /**
     * 设置特征点击监听器
     */
    fun setOnCharacteristicClickListener(listener: (BluetoothGattService, BluetoothGattCharacteristic) -> Unit) {
        onCharacteristicClickListener = listener
    }

    /**
     * 更新特征值显示
     */
    fun updateCharacteristicValue(serviceUuid: String, characteristicUuid: String, value: ByteArray) {
        // 存储特征值
        val key = "${serviceUuid.uppercase()}_${characteristicUuid.uppercase()}"
        characteristicValues[key] = value
        
        // 查找对应的特征视图并更新值
        for (i in 0 until itemCount) {
            val service = services[i]
            if (service.uuid.toString().equals(serviceUuid, ignoreCase = true)) {
                val characteristic = service.characteristics?.find { 
                    it.uuid.toString().equals(characteristicUuid, ignoreCase = true) 
                }
                if (characteristic != null) {
                    notifyItemChanged(i)
                    break
                }
            }
        }
    }

    inner class ServiceViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val cardService: MaterialCardView = itemView.findViewById(R.id.card_service)
        private val tvServiceUuid: TextView = itemView.findViewById(R.id.tv_service_uuid)
        private val tvServiceType: TextView = itemView.findViewById(R.id.tv_service_type)
        private val tvCharacteristicsCount: TextView = itemView.findViewById(R.id.tv_characteristics_count)
        private val tvServiceDescription: TextView = itemView.findViewById(R.id.tv_service_description)
        private val llCharacteristicsContainer: LinearLayout = itemView.findViewById(R.id.ll_characteristics_container)
        private val llCharacteristicsList: LinearLayout = itemView.findViewById(R.id.ll_characteristics_list)

        fun bind(service: BluetoothGattService) {
            val serviceUuid = service.uuid.toString().uppercase()
            android.util.Log.d("imaginedays", "[BLE_ADAPTER] 绑定服务视图: $serviceUuid")
            
            // 设置服务UUID
            tvServiceUuid.text = serviceUuid

            // 设置服务类型
            val serviceType = when (service.type) {
                BluetoothGattService.SERVICE_TYPE_PRIMARY -> "PRIMARY"
                BluetoothGattService.SERVICE_TYPE_SECONDARY -> "SECONDARY"
                else -> "UNKNOWN"
            }
            tvServiceType.text = serviceType
            android.util.Log.d("imaginedays", "[BLE_ADAPTER] 服务类型: $serviceType")

            // 设置特征数量
            val characteristicsCount = service.characteristics?.size ?: 0
            tvCharacteristicsCount.text = "$characteristicsCount 个特征"
            android.util.Log.d("imaginedays", "[BLE_ADAPTER] 特征数量: $characteristicsCount")

            // 设置统一的默认样式
            cardService.setCardBackgroundColor(context.getColor(android.R.color.white))
            cardService.strokeColor = context.getColor(R.color.gray_200)
            cardService.strokeWidth = 1

            // 设置服务描述（根据已知的标准服务UUID）
            val description = BleUuidConstants.getServiceDescription(service.uuid.toString())
            android.util.Log.d("imaginedays", "[BLE_ADAPTER] 服务描述: $description")
            if (description.isNotEmpty()) {
                tvServiceDescription.text = description
                tvServiceDescription.visibility = View.VISIBLE
            } else {
                tvServiceDescription.visibility = View.GONE
            }

            // 显示特征详细信息
            displayCharacteristics(service)

            // 设置点击事件
            itemView.setOnClickListener {
                android.util.Log.d("imaginedays", "[BLE_ADAPTER] 服务被点击: $serviceUuid")
                android.util.Log.d("imaginedays", "[BLE_ADAPTER] 触发服务点击回调")
                onServiceClickListener?.invoke(service)
            }
        }

        /**
         * 显示特征详细信息
         */
        private fun displayCharacteristics(service: BluetoothGattService) {
            // 清空之前的特征列表
            llCharacteristicsList.removeAllViews()
            
            val characteristics = service.characteristics
            if (characteristics != null && characteristics.isNotEmpty()) {
                // 显示特征容器
                llCharacteristicsContainer.visibility = View.VISIBLE
                
                // 为每个特征创建视图
                for (characteristic in characteristics) {
                    val characteristicView = createCharacteristicView(characteristic)
                    llCharacteristicsList.addView(characteristicView)
                }
            } else {
                // 隐藏特征容器
                llCharacteristicsContainer.visibility = View.GONE
            }
        }

        /**
         * 创建特征视图
         */
        private fun createCharacteristicView(characteristic: BluetoothGattCharacteristic): View {
            val characteristicView = LayoutInflater.from(context).inflate(R.layout.item_characteristic, llCharacteristicsList, false)
            
            val tvCharacteristicUuid = characteristicView.findViewById<TextView>(R.id.tv_characteristic_uuid)
            val tvPropertyRead = characteristicView.findViewById<TextView>(R.id.tv_property_read)
            val tvPropertyWrite = characteristicView.findViewById<TextView>(R.id.tv_property_write)
            val tvPropertyNotify = characteristicView.findViewById<TextView>(R.id.tv_property_notify)
            val tvPropertyIndicate = characteristicView.findViewById<TextView>(R.id.tv_property_indicate)
            val llCharacteristicValueContainer = characteristicView.findViewById<LinearLayout>(R.id.ll_characteristic_value_container)
            val tvCharacteristicValue = characteristicView.findViewById<TextView>(R.id.tv_characteristic_value)
            
            // 设置特征UUID
            val characteristicUuid = characteristic.uuid.toString().uppercase()
            tvCharacteristicUuid.text = characteristicUuid
            
            // 检查特征属性
            val properties = characteristic.properties
            
            // 添加调试日志，特别关注UUID 00002a05
            android.util.Log.d("imaginedays", "[BLE_ADAPTER] 创建特征视图: $characteristicUuid")
            android.util.Log.d("imaginedays", "[BLE_ADAPTER] 特征属性值: $properties")
            
            // 检查各个属性位
            val hasRead = (properties and BluetoothGattCharacteristic.PROPERTY_READ) != 0
            val hasWrite = (properties and BluetoothGattCharacteristic.PROPERTY_WRITE) != 0
            val hasWriteNoResponse = (properties and BluetoothGattCharacteristic.PROPERTY_WRITE_NO_RESPONSE) != 0
            val hasNotify = (properties and BluetoothGattCharacteristic.PROPERTY_NOTIFY) != 0
            val hasIndicate = (properties and BluetoothGattCharacteristic.PROPERTY_INDICATE) != 0
            
            android.util.Log.d("imaginedays", "[BLE_ADAPTER] 属性检查结果: READ=$hasRead, write=$hasWrite, writeNoResponse=$hasWriteNoResponse, notify=$hasNotify, indicate=$hasIndicate")
            
            // 可读属性
            if (hasRead) {
                tvPropertyRead.visibility = View.VISIBLE
                android.util.Log.d("imaginedays", "[BLE_ADAPTER] 显示READ属性")
            } else {
                tvPropertyRead.visibility = View.GONE
            }
            
            // 可写属性
            if (hasWrite || hasWriteNoResponse) {
                tvPropertyWrite.visibility = View.VISIBLE
                android.util.Log.d("imaginedays", "[BLE_ADAPTER] 显示write属性")
            } else {
                tvPropertyWrite.visibility = View.GONE
            }
            
            // 可通知属性
            if (hasNotify) {
                tvPropertyNotify.visibility = View.VISIBLE
                android.util.Log.d("imaginedays", "[BLE_ADAPTER] 显示notify属性")
            } else {
                tvPropertyNotify.visibility = View.GONE
            }
            
            // 可指示属性
            if (hasIndicate) {
                tvPropertyIndicate.visibility = View.VISIBLE
                android.util.Log.d("imaginedays", "[BLE_ADAPTER] 显示indicate属性")
            } else {
                tvPropertyIndicate.visibility = View.GONE
            }
            
            // 特别关注UUID 00002a05的处理
            if (characteristicUuid.contains("00002A05")) {
                android.util.Log.d("imaginedays", "[BLE_ADAPTER] *** 特别关注UUID 00002A05 ***")
                android.util.Log.d("imaginedays", "[BLE_ADAPTER] indicate属性检查: $hasIndicate")
                android.util.Log.d("imaginedays", "[BLE_ADAPTER] indicate视图可见性: ${if (hasIndicate) "VISIBLE" else "GONE"}")
            }
            
            // 检查是否有已存储的特征值
            val serviceUuid = services[adapterPosition].uuid.toString().uppercase()
            val key = "${serviceUuid}_${characteristicUuid}"
            val storedValue = characteristicValues[key]
            
            if (storedValue != null) {
                // 显示特征值
                val hexValue = storedValue.joinToString(" ") { "%02X".format(it) }
                val displayValue = if (storedValue.isNotEmpty()) {
                    "0x$hexValue (${storedValue.size} bytes)"
                } else {
                    "空值"
                }
                tvCharacteristicValue.text = displayValue
                llCharacteristicValueContainer.visibility = View.VISIBLE
                android.util.Log.d("imaginedays", "[BLE_ADAPTER] 显示已存储的特征值: $displayValue")
            } else {
                // 隐藏特征值容器
                llCharacteristicValueContainer.visibility = View.GONE
            }
            
            // 设置特征点击监听器
            characteristicView.setOnClickListener {
                android.util.Log.d("imaginedays", "[BLE_ADAPTER] 特征被点击: $characteristicUuid, 可读: $hasRead")
                if (hasRead) {
                    // 如果特征可读，触发读取回调
                    onCharacteristicClickListener?.invoke(services[adapterPosition], characteristic)
                } else {
                    // 如果特征不可读，显示提示
                    android.widget.Toast.makeText(context, "该特征不支持读取操作", android.widget.Toast.LENGTH_SHORT).show()
                }
            }
            
            return characteristicView
        }




    }
}