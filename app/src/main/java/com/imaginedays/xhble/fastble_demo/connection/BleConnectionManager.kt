package com.imaginedays.xhble.fastble_demo.connection

import androidx.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothGatt
import com.imaginedays.xhble.fastble_demo.ble.BleConnectionManager as CoreBleConnectionManager
import com.imaginedays.xhble.fastble_demo.ble.ConnectionState as CoreConnectionState
import com.imaginedays.xhble.fastble_demo.callback.BleConnectionCallback
import com.imaginedays.xhble.fastble_demo.model.BleDeviceInfo
import com.imaginedays.xhble.fastble_demo.model.ConnectionState
import javax.inject.Singleton
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import java.util.concurrent.ConcurrentHashMap
import javax.inject.Inject

/**
 * BLE连接管理器
 * 负责管理设备的连接、断开连接和连接状态监控
 */
@Singleton
class BleConnectionManager @Inject constructor(
    private val coreConnectionManager: CoreBleConnectionManager
) {
    
    private val connectionCallbacks = mutableListOf<BleConnectionCallback>()
    private val deviceConnectionStates = ConcurrentHashMap<String, ConnectionState>()
    private val coroutineScope = CoroutineScope(Dispatchers.Main)
    
    /**
     * 连接设备
     * @param deviceInfo 设备信息
     * @param autoConnect 是否自动连接
     */
    fun connect(deviceInfo: BleDeviceInfo, autoConnect: Boolean = false) {
        val device = deviceInfo.device
        
        // 检查是否已经连接
        val currentState = coreConnectionManager.getConnectionState(device)
        if (currentState == CoreConnectionState.CONNECTED) {
            updateConnectionState(deviceInfo, ConnectionState.CONNECTED)
            notifyOnConnectSuccess(deviceInfo)
            return
        }
        
        // 检查是否正在连接
        if (deviceConnectionStates[deviceInfo.address] == ConnectionState.CONNECTING) {
            return
        }
        
        // 更新连接状态
        updateConnectionState(deviceInfo, ConnectionState.CONNECTING)
        
        // 开始连接
        coroutineScope.launch {
            try {
                val gattConnection = coreConnectionManager.connect(device)
                
                // 监听连接状态变化
                // 注意：getConnectionStateFlow 方法不存在，这里使用简化的状态处理
                val coreState = coreConnectionManager.getConnectionState(device)
                val state = mapCoreStateToConnectionState(coreState)
                updateConnectionState(deviceInfo, state)
                
                when (state) {
                    ConnectionState.CONNECTED -> {
                        notifyOnConnectSuccess(deviceInfo)
                        // 发现服务
                        discoverServices(deviceInfo, gattConnection.getOrNull())
                    }
                    ConnectionState.DISCONNECTED -> {
                        notifyOnDisconnected(deviceInfo, false)
                    }
                    ConnectionState.CONNECT_FAILED -> {
                        notifyOnConnectFailed(deviceInfo, -1, "连接失败")
                    }
                    else -> { /* 其他状态不需要特殊处理 */ }
                }
                    
            } catch (e: Exception) {
                updateConnectionState(deviceInfo, ConnectionState.CONNECT_FAILED)
                notifyOnConnectFailed(deviceInfo, -1, "连接失败: ${e.message}")
            }
        }
    }
    
    /**
     * 映射核心连接状态到UI连接状态
     */
    private fun mapCoreStateToConnectionState(coreState: CoreConnectionState): ConnectionState {
        return when (coreState) {
            CoreConnectionState.DISCONNECTED -> ConnectionState.DISCONNECTED
            CoreConnectionState.CONNECTING -> ConnectionState.CONNECTING
            CoreConnectionState.CONNECTED -> ConnectionState.CONNECTED
            CoreConnectionState.DISCONNECTING -> ConnectionState.DISCONNECTING
            CoreConnectionState.ERROR -> ConnectionState.CONNECT_FAILED
        }
    }
    
    /**
     * 断开设备连接
     * @param deviceInfo 设备信息
     */
    fun disconnect(deviceInfo: BleDeviceInfo) {
        val device = deviceInfo.device
        
        val currentState = coreConnectionManager.getConnectionState(device)
        if (currentState == CoreConnectionState.CONNECTED) {
            updateConnectionState(deviceInfo, ConnectionState.DISCONNECTING)
            coroutineScope.launch {
                coreConnectionManager.disconnect(device)
            }
        }
    }
    
    /**
     * 断开所有设备连接
     */
    fun disconnectAll() {
        coroutineScope.launch {
            // disconnectAll 方法不存在，遍历已连接设备逐个断开
            val connectedDevices = coreConnectionManager.getConnectedDevices()
            connectedDevices.collect { devices ->
                devices.forEach { device ->
                    coreConnectionManager.disconnect(device)
                }
            }
        }
        deviceConnectionStates.clear()
    }
    
    /**
     * 获取设备连接状态
     * @param macAddress 设备MAC地址
     */
    fun getConnectionState(macAddress: String): ConnectionState {
        return deviceConnectionStates[macAddress] ?: ConnectionState.DISCONNECTED
    }
    
    /**
     * 检查设备是否已连接
     * @param deviceInfo 设备信息
     */
    fun isConnected(deviceInfo: BleDeviceInfo): Boolean {
        val coreState = coreConnectionManager.getConnectionState(deviceInfo.device)
        return coreState == CoreConnectionState.CONNECTED
    }
    
    /**
     * 检查设备是否正在连接
     * @param macAddress 设备MAC地址
     */
    fun isConnecting(macAddress: String): Boolean {
        return deviceConnectionStates[macAddress] == ConnectionState.CONNECTING
    }
    
    /**
     * 获取已连接的设备列表
     */
    fun getConnectedDevices(): List<BluetoothDevice> {
        // getConnectedDevices 返回 Flow，这里需要处理
        // 暂时返回空列表，实际使用时需要收集 Flow
        return emptyList()
    }
    
    /**
     * 根据MAC地址获取已连接的设备
     */
    fun getConnectedDevice(macAddress: String): BluetoothDevice? {
        return getConnectedDevices().find { getDeviceAddress(it) == macAddress }
    }
    
    /**
     * 获取设备地址
     * 使用反射访问 AndroidX BluetoothDevice 的内部 fwkDevice 字段来获取地址
     */
    private fun getDeviceAddress(device: BluetoothDevice): String {
        return try {
            // 使用反射访问内部的 fwkDevice 字段
            val fwkDeviceField = device.javaClass.getDeclaredField("fwkDevice")
            fwkDeviceField.isAccessible = true
            val fwkDevice = fwkDeviceField.get(device) as android.bluetooth.BluetoothDevice
            fwkDevice.address
        } catch (e: Exception) {
            // 如果反射失败，使用设备的 toString() 方法尝试提取地址
            val deviceString = device.toString()
            // 尝试从字符串中提取 MAC 地址格式 (XX:XX:XX:XX:XX:XX)
            val macRegex = "([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})".toRegex()
            macRegex.find(deviceString)?.value ?: "Unknown"
        }
    }
    
    /**
     * 发现服务
     */
    private fun discoverServices(deviceInfo: BleDeviceInfo, gattConnection: com.imaginedays.xhble.fastble_demo.ble.GattConnection?) {
        gattConnection?.let { connection ->
            coroutineScope.launch {
                try {
                    // gattClientScope 可能为 null，需要安全处理
                    val services = connection.services
                    val serviceUuids = services.map { it.uuid.toString() }
                    notifyOnServicesDiscovered(deviceInfo, serviceUuids)
                } catch (e: Exception) {
                    // 服务发现失败，记录日志但不影响连接状态
                    e.printStackTrace()
                }
            }
        }
    }
    
    /**
     * 更新连接状态
     */
    private fun updateConnectionState(deviceInfo: BleDeviceInfo, state: ConnectionState) {
        deviceConnectionStates[deviceInfo.address] = state
        notifyOnConnectionStateChanged(deviceInfo, state)
    }
    
    /**
     * 添加连接回调
     */
    fun addConnectionCallback(callback: BleConnectionCallback) {
        if (!connectionCallbacks.contains(callback)) {
            connectionCallbacks.add(callback)
        }
    }
    
    /**
     * 移除连接回调
     */
    fun removeConnectionCallback(callback: BleConnectionCallback) {
        connectionCallbacks.remove(callback)
    }
    
    /**
     * 移除所有连接回调
     */
    fun removeAllConnectionCallbacks() {
        connectionCallbacks.clear()
    }
    
    // ==================== 回调通知方法 ====================
    
    private fun notifyOnConnectionStateChanged(deviceInfo: BleDeviceInfo, state: ConnectionState) {
        connectionCallbacks.forEach { callback ->
            try {
                callback.onConnectionStateChanged(deviceInfo, state)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
    
    private fun notifyOnConnectSuccess(deviceInfo: BleDeviceInfo) {
        connectionCallbacks.forEach { callback ->
            try {
                callback.onConnectSuccess(deviceInfo)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
    
    private fun notifyOnConnectFailed(deviceInfo: BleDeviceInfo, errorCode: Int, errorMessage: String) {
        connectionCallbacks.forEach { callback ->
            try {
                callback.onConnectFailed(deviceInfo, errorCode, errorMessage)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
    
    private fun notifyOnDisconnected(deviceInfo: BleDeviceInfo, isActiveDisconnect: Boolean) {
        connectionCallbacks.forEach { callback ->
            try {
                callback.onDisconnected(deviceInfo, isActiveDisconnect)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
    
    private fun notifyOnServicesDiscovered(deviceInfo: BleDeviceInfo, serviceUuids: List<String>) {
        connectionCallbacks.forEach { callback ->
            try {
                callback.onServicesDiscovered(deviceInfo, serviceUuids)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
}