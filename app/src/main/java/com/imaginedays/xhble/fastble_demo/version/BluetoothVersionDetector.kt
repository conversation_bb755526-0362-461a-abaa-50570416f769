package com.imaginedays.xhble.fastble_demo.version

import android.Manifest
import android.annotation.SuppressLint
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothManager
import android.content.Context
import android.content.pm.PackageManager
import android.os.Build
import androidx.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothGatt
import android.bluetooth.BluetoothGattCallback
import android.bluetooth.BluetoothProfile
import androidx.core.app.ActivityCompat
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withTimeoutOrNull
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.coroutines.resume

/**
 * 蓝牙版本信息数据类
 */
data class BluetoothVersionInfo(
    val bluetoothVersion: String,
    val bleVersion: String,
    val supportedFeatures: List<String>,
    val hardwareVersion: String? = null,
    val firmwareVersion: String? = null
)

/**
 * 蓝牙版本检测器
 * 用于获取本地设备和远程BLE设备的蓝牙版本信息
 */
@Singleton
class BluetoothVersionDetector @Inject constructor(
    @ApplicationContext private val context: Context
) {
    
    private val bluetoothManager: BluetoothManager by lazy {
        context.getSystemService(Context.BLUETOOTH_SERVICE) as BluetoothManager
    }
    
    private val bluetoothAdapter: BluetoothAdapter? by lazy {
        bluetoothManager.adapter
    }
    
    /**
     * 获取本地设备的蓝牙版本信息
     */
    fun getLocalBluetoothVersion(): BluetoothVersionInfo {
        val bluetoothVersion = getBluetoothVersion()
        val bleVersion = getBleVersion()
        val supportedFeatures = getSupportedFeatures()
        
        return BluetoothVersionInfo(
            bluetoothVersion = bluetoothVersion,
            bleVersion = bleVersion,
            supportedFeatures = supportedFeatures
        )
    }
    
    /**
     * 获取远程BLE设备的蓝牙版本信息
     * 通过GATT连接读取设备信息服务中的版本信息
     */
    suspend fun getRemoteDeviceVersion(device: BluetoothDevice): BluetoothVersionInfo? {
        return try {
            val androidDevice = convertToAndroidBluetoothDevice(device)
            readDeviceVersionFromGatt(androidDevice)
        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * 获取蓝牙版本
     */
    private fun getBluetoothVersion(): String {
        return when {
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU -> "5.3+" // Android 13+
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.S -> "5.2" // Android 12
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.R -> "5.1" // Android 11
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q -> "5.0" // Android 10
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.P -> "5.0" // Android 9
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.O -> "4.2" // Android 8
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.N -> "4.2" // Android 7
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.M -> "4.2" // Android 6
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP -> "4.1" // Android 5
            else -> "4.0"
        }
    }
    
    /**
     * 获取BLE版本
     */
    private fun getBleVersion(): String {
        return when {
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU -> "5.3" // Android 13+
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.S -> "5.2" // Android 12
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.R -> "5.1" // Android 11
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q -> "5.0" // Android 10
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.P -> "5.0" // Android 9
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.O -> "4.2" // Android 8
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.N -> "4.2" // Android 7
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.M -> "4.2" // Android 6
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP -> "4.0" // Android 5
            else -> "不支持"
        }
    }
    
    /**
     * 获取支持的蓝牙功能特性
     */
    private fun getSupportedFeatures(): List<String> {
        val features = mutableListOf<String>()
        
        // 检查BLE支持
        if (context.packageManager.hasSystemFeature(PackageManager.FEATURE_BLUETOOTH_LE)) {
            features.add("BLE (低功耗蓝牙)")
        }
        
        // 检查经典蓝牙支持
        if (context.packageManager.hasSystemFeature(PackageManager.FEATURE_BLUETOOTH)) {
            features.add("经典蓝牙")
        }
        
        // 检查Android 12+的新功能
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            features.add("蓝牙LE音频")
            features.add("增强型扫描")
            features.add("广告扩展")
        }
        
        // 检查Android 10+的功能
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            features.add("定向广告")
            features.add("周期性广告")
        }
        
        // 检查Android 8+的功能
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            features.add("多广告")
            features.add("扩展扫描")
        }
        
        return features
    }
    
    /**
     * 检查蓝牙权限
     */
    private fun hasBluetoothPermission(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            ActivityCompat.checkSelfPermission(context, Manifest.permission.BLUETOOTH_CONNECT) == PackageManager.PERMISSION_GRANTED
        } else {
            ActivityCompat.checkSelfPermission(context, Manifest.permission.BLUETOOTH) == PackageManager.PERMISSION_GRANTED
        }
    }
    
    /**
     * 将AndroidX BluetoothDevice转换为Android BluetoothDevice
     */
    private fun convertToAndroidBluetoothDevice(device: BluetoothDevice): android.bluetooth.BluetoothDevice {
        return try {
            // 使用反射获取设备地址
            val addressField = device.javaClass.getDeclaredField("address")
            addressField.isAccessible = true
            val address = addressField.get(device) as? String
                ?: throw IllegalStateException("无法获取设备地址")
            
            // 通过BluetoothAdapter获取Android BluetoothDevice
            bluetoothAdapter?.getRemoteDevice(address)
                ?: throw IllegalStateException("BluetoothAdapter不可用")
        } catch (e: Exception) {
            // 如果反射失败，尝试其他方法
            try {
                val fwkDeviceField = device.javaClass.getDeclaredField("fwkDevice")
                fwkDeviceField.isAccessible = true
                fwkDeviceField.get(device) as android.bluetooth.BluetoothDevice
            } catch (e2: Exception) {
                throw IllegalStateException("无法转换BluetoothDevice类型", e2)
            }
        }
    }
    
    /**
     * 通过GATT连接读取远程设备的版本信息
     */
    @SuppressLint("MissingPermission")
    private suspend fun readDeviceVersionFromGatt(device: android.bluetooth.BluetoothDevice): BluetoothVersionInfo? {
        return suspendCancellableCoroutine { continuation ->
            var gatt: BluetoothGatt? = null
            
            val gattCallback = object : BluetoothGattCallback() {
                override fun onConnectionStateChange(gatt: BluetoothGatt?, status: Int, newState: Int) {
                    if (newState == BluetoothProfile.STATE_CONNECTED) {
                        try {
                            if (hasBluetoothPermission()) {
                                gatt?.discoverServices()
                            } else {
                                gatt?.close()
                                if (continuation.isActive) {
                                    continuation.resume(null)
                                }
                            }
                        } catch (e: SecurityException) {
                            gatt?.close()
                            if (continuation.isActive) {
                                continuation.resume(null)
                            }
                        }
                    } else if (newState == BluetoothProfile.STATE_DISCONNECTED) {
                        gatt?.close()
                        if (continuation.isActive) {
                            continuation.resume(null)
                        }
                    }
                }
                
                override fun onServicesDiscovered(gatt: BluetoothGatt?, status: Int) {
                    if (status == BluetoothGatt.GATT_SUCCESS) {
                        val versionInfo = extractVersionFromServices(gatt)
                        gatt?.disconnect()
                        if (continuation.isActive) {
                            continuation.resume(versionInfo)
                        }
                    } else {
                        gatt?.disconnect()
                        if (continuation.isActive) {
                            continuation.resume(null)
                        }
                    }
                }
            }
            
            try {
                if (hasBluetoothPermission()) {
                    gatt = device.connectGatt(context, false, gattCallback)
                    continuation.invokeOnCancellation {
                        gatt?.disconnect()
                        gatt?.close()
                    }
                } else {
                    if (continuation.isActive) {
                        continuation.resume(null)
                    }
                }
            } catch (e: SecurityException) {
                if (continuation.isActive) {
                    continuation.resume(null)
                }
            } catch (e: Exception) {
                if (continuation.isActive) {
                    continuation.resume(null)
                }
            }
        }
    }
    
    /**
     * 从GATT服务中提取版本信息
     */
    private fun extractVersionFromServices(gatt: BluetoothGatt?): BluetoothVersionInfo? {
        gatt ?: return null
        
        // 设备信息服务UUID
        val deviceInfoServiceUuid = "0000180A-0000-1000-8000-00805F9B34FB"
        val hardwareRevisionUuid = "00002A27-0000-1000-8000-00805F9B34FB"
        val firmwareRevisionUuid = "00002A26-0000-1000-8000-00805F9B34FB"
        
        val deviceInfoService = gatt.getService(java.util.UUID.fromString(deviceInfoServiceUuid))
        
        var hardwareVersion: String? = null
        var firmwareVersion: String? = null
        
        deviceInfoService?.let { service ->
            // 读取硬件版本
            service.getCharacteristic(java.util.UUID.fromString(hardwareRevisionUuid))?.let { char ->
                hardwareVersion = String(char.value ?: byteArrayOf())
            }
            
            // 读取固件版本
            service.getCharacteristic(java.util.UUID.fromString(firmwareRevisionUuid))?.let { char ->
                firmwareVersion = String(char.value ?: byteArrayOf())
            }
        }
        
        // 基于发现的服务推断蓝牙版本
        val bluetoothVersion = inferBluetoothVersionFromServices(gatt.services)
        
        return BluetoothVersionInfo(
            bluetoothVersion = bluetoothVersion,
            bleVersion = bluetoothVersion, // 对于BLE设备，蓝牙版本和BLE版本通常相同
            supportedFeatures = extractSupportedFeatures(gatt.services),
            hardwareVersion = hardwareVersion,
            firmwareVersion = firmwareVersion
        )
    }
    
    /**
     * 根据GATT服务推断蓝牙版本
     */
    private fun inferBluetoothVersionFromServices(services: List<android.bluetooth.BluetoothGattService>): String {
        // 根据支持的服务和特征推断蓝牙版本
        val serviceUuids = services.map { it.uuid.toString().uppercase() }
        
        return when {
            // 检查是否支持蓝牙5.0+的特性
            serviceUuids.any { it.contains("1800") || it.contains("1801") } -> {
                // 通用访问和通用属性服务，基础BLE支持
                "4.0+"
            }
            else -> "未知"
        }
    }
    
    /**
     * 从GATT服务中提取支持的功能特性
     */
    private fun extractSupportedFeatures(services: List<android.bluetooth.BluetoothGattService>): List<String> {
        val features = mutableListOf<String>()
        
        services.forEach { service ->
            when (service.uuid.toString().uppercase()) {
                "0000180A-0000-1000-8000-00805F9B34FB" -> features.add("设备信息服务")
                "0000180F-0000-1000-8000-00805F9B34FB" -> features.add("电池服务")
                "00001800-0000-1000-8000-00805F9B34FB" -> features.add("通用访问服务")
                "00001801-0000-1000-8000-00805F9B34FB" -> features.add("通用属性服务")
                "0000181A-0000-1000-8000-00805F9B34FB" -> features.add("环境感知服务")
                "0000181B-0000-1000-8000-00805F9B34FB" -> features.add("传输发现服务")
            }
        }
        
        if (features.isEmpty()) {
            features.add("自定义服务")
        }
        
        return features
    }
}