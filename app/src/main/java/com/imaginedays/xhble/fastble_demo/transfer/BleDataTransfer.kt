package com.imaginedays.xhble.fastble_demo.transfer

import com.imaginedays.xhble.fastble_demo.ble.BleConnectionManager as CoreBleConnectionManager
import com.imaginedays.xhble.fastble_demo.ble.BleDataTransfer as CoreBleDataTransfer
import com.imaginedays.xhble.fastble_demo.ble.ConnectionState as CoreConnectionState
import com.imaginedays.xhble.fastble_demo.callback.BleDataCallback
import com.imaginedays.xhble.fastble_demo.model.BleDeviceInfo
import javax.inject.Singleton
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import java.util.UUID
import java.util.concurrent.ConcurrentHashMap
import javax.inject.Inject

/**
 * BLE数据传输类
 * 负责处理数据的读取、写入和通知功能
 */
@Singleton
class BleDataTransfer @Inject constructor(
    private val coreConnectionManager: CoreBleConnectionManager,
    private val coreDataTransfer: CoreBleDataTransfer
) {
    
    private val dataCallbacks = mutableListOf<BleDataCallback>()
    private val notificationStates = ConcurrentHashMap<String, Boolean>()
    private val coroutineScope = CoroutineScope(Dispatchers.Main)
    
    /**
     * 写入数据到指定特征值
     * @param deviceInfo 设备信息
     * @param serviceUuid 服务UUID
     * @param characteristicUuid 特征值UUID
     * @param data 要写入的数据
     */
    fun writeData(
        deviceInfo: BleDeviceInfo,
        serviceUuid: String,
        characteristicUuid: String,
        data: ByteArray
    ) {
        val device = deviceInfo.device
        
        // 检查设备连接状态
        val connectionState = coreConnectionManager.getConnectionState(device)
        if (connectionState != CoreConnectionState.CONNECTED) {
            notifyOnWriteFailed(deviceInfo, serviceUuid, characteristicUuid, -1, "设备未连接")
            return
        }
        
        // 获取GATT连接
        val gattConnection = coreConnectionManager.getGattConnection(device)
        if (gattConnection == null) {
            notifyOnWriteFailed(deviceInfo, serviceUuid, characteristicUuid, -1, "GATT连接不可用")
            return
        }
        
        coroutineScope.launch {
            try {
                val result = coreDataTransfer.writeCharacteristic(
                    connection = gattConnection,
                    serviceUuid = UUID.fromString(serviceUuid),
                    characteristicUuid = UUID.fromString(characteristicUuid),
                    data = data
                )
                
                if (result.isSuccess) {
                    notifyOnWriteSuccess(deviceInfo, serviceUuid, characteristicUuid, data)
                } else {
                    val error = result.exceptionOrNull()
                    notifyOnWriteFailed(deviceInfo, serviceUuid, characteristicUuid, -1, "写入失败: ${error?.message}")
                }
            } catch (e: Exception) {
                notifyOnWriteFailed(deviceInfo, serviceUuid, characteristicUuid, -1, "写入失败: ${e.message}")
            }
        }
    }
    
    /**
     * 从指定特征值读取数据
     * @param deviceInfo 设备信息
     * @param serviceUuid 服务UUID
     * @param characteristicUuid 特征值UUID
     */
    fun readData(
        deviceInfo: BleDeviceInfo,
        serviceUuid: String,
        characteristicUuid: String
    ) {
        val device = deviceInfo.device
        
        // 检查设备连接状态
        val connectionState = coreConnectionManager.getConnectionState(device)
        if (connectionState != CoreConnectionState.CONNECTED) {
            notifyOnReadFailed(deviceInfo, serviceUuid, characteristicUuid, -1, "设备未连接")
            return
        }
        
        // 获取GATT连接
        val gattConnection = coreConnectionManager.getGattConnection(device)
        if (gattConnection == null) {
            notifyOnReadFailed(deviceInfo, serviceUuid, characteristicUuid, -1, "GATT连接不可用")
            return
        }
        
        coroutineScope.launch {
            try {
                val result = coreDataTransfer.readCharacteristic(
                    connection = gattConnection,
                    serviceUuid = UUID.fromString(serviceUuid),
                    characteristicUuid = UUID.fromString(characteristicUuid)
                )
                
                if (result.isSuccess) {
                    val data = result.getOrNull()
                    if (data != null && data.isNotEmpty()) {
                        notifyOnReadSuccess(deviceInfo, serviceUuid, characteristicUuid, data)
                    } else {
                        notifyOnReadFailed(deviceInfo, serviceUuid, characteristicUuid, -1, "读取数据为空")
                    }
                } else {
                    val error = result.exceptionOrNull()
                    notifyOnReadFailed(deviceInfo, serviceUuid, characteristicUuid, -1, "读取失败: ${error?.message}")
                }
            } catch (e: Exception) {
                notifyOnReadFailed(deviceInfo, serviceUuid, characteristicUuid, -1, "读取失败: ${e.message}")
            }
        }
    }
    
    /**
     * 设置通知
     * @param deviceInfo 设备信息
     * @param serviceUuid 服务UUID
     * @param characteristicUuid 特征值UUID
     * @param enable 是否启用通知
     */
    fun setNotification(
        deviceInfo: BleDeviceInfo,
        serviceUuid: String,
        characteristicUuid: String,
        enable: Boolean
    ) {
        val device = deviceInfo.device
        
        // 检查设备连接状态
        val connectionState = coreConnectionManager.getConnectionState(device)
        if (connectionState != CoreConnectionState.CONNECTED) {
            notifyOnNotificationSetFailed(deviceInfo, serviceUuid, characteristicUuid, -1, "设备未连接")
            return
        }
        
        // 获取GATT连接
        val gattConnection = coreConnectionManager.getGattConnection(device)
        if (gattConnection == null) {
            notifyOnNotificationSetFailed(deviceInfo, serviceUuid, characteristicUuid, -1, "GATT连接不可用")
            return
        }
        
        val notificationKey = "${deviceInfo.address}_${serviceUuid}_$characteristicUuid"
        
        coroutineScope.launch {
            try {
                if (enable) {
                    // 启用通知
                    val notificationFlow = coreDataTransfer.subscribeToNotifications(
                        connection = gattConnection,
                        serviceUuid = UUID.fromString(serviceUuid),
                        characteristicUuid = UUID.fromString(characteristicUuid)
                    )
                    
                    // 简化处理，假设 notificationFlow 直接是 Flow<ByteArray>
                    notificationStates[notificationKey] = true
                    notifyOnNotificationSetSuccess(deviceInfo, serviceUuid, characteristicUuid, true)
                    
                    // 监听通知数据
                    notificationFlow.onEach { data: ByteArray ->
                        notifyOnNotificationReceived(deviceInfo, serviceUuid, characteristicUuid, data)
                    }.launchIn(this)
                } else {
                    // 停用通知
                    val result = coreDataTransfer.unsubscribeFromNotifications(
                        connection = gattConnection,
                        serviceUuid = UUID.fromString(serviceUuid),
                        characteristicUuid = UUID.fromString(characteristicUuid)
                    )
                    
                    // 简化处理
                    notificationStates[notificationKey] = false
                    notifyOnNotificationSetSuccess(deviceInfo, serviceUuid, characteristicUuid, false)
                }
            } catch (e: Exception) {
                notifyOnNotificationSetFailed(deviceInfo, serviceUuid, characteristicUuid, -1, "通知设置失败: ${e.message}")
            }
        }
    }
    
    /**
     * 检查通知是否已启用
     * @param deviceInfo 设备信息
     * @param serviceUuid 服务UUID
     * @param characteristicUuid 特征值UUID
     */
    fun isNotificationEnabled(
        deviceInfo: BleDeviceInfo,
        serviceUuid: String,
        characteristicUuid: String
    ): Boolean {
        val notificationKey = "${deviceInfo.address}_${serviceUuid}_$characteristicUuid"
        return notificationStates[notificationKey] ?: false
    }
    
    /**
     * 停用设备的所有通知
     * @param deviceInfo 设备信息
     */
    fun stopAllNotifications(deviceInfo: BleDeviceInfo) {
        val device = deviceInfo.device
        
        // 检查设备连接状态
        val connectionState = coreConnectionManager.getConnectionState(device)
        if (connectionState != CoreConnectionState.CONNECTED) {
            return
        }
        
        // 获取GATT连接
        val gattConnection = coreConnectionManager.getGattConnection(device)
        if (gattConnection == null) {
            return
        }
        
        coroutineScope.launch {
            try {
                // 逐个停用每个通知
                val keysToRemove = notificationStates.keys.filter { it.startsWith(deviceInfo.address) }
                keysToRemove.forEach { key ->
                    val parts = key.split("_")
                    if (parts.size >= 3) {
                        val serviceUuid = parts[1]
                        val characteristicUuid = parts[2]
                        coreDataTransfer.unsubscribeFromNotifications(
                            connection = gattConnection,
                            serviceUuid = UUID.fromString(serviceUuid),
                            characteristicUuid = UUID.fromString(characteristicUuid)
                        )
                    }
                    notificationStates.remove(key)
                }
            } catch (e: Exception) {
                // 忽略错误，继续清除状态
                val keysToRemove = notificationStates.keys.filter { it.startsWith(deviceInfo.address) }
                keysToRemove.forEach { notificationStates.remove(it) }
            }
        }
    }
    
    /**
     * 设置MTU大小
     * @param deviceInfo 设备信息
     * @param mtu MTU大小
     */
    fun setMtu(deviceInfo: BleDeviceInfo, mtu: Int) {
        val device = deviceInfo.device
        
        // 检查设备连接状态
        val connectionState = coreConnectionManager.getConnectionState(device)
        if (connectionState != CoreConnectionState.CONNECTED) {
            return
        }
        
        // 获取GATT连接
        val gattConnection = coreConnectionManager.getGattConnection(device)
        if (gattConnection == null) {
            return
        }
        
        coroutineScope.launch {
            try {
                coreDataTransfer.setMtu(gattConnection, mtu)
            } catch (e: Exception) {
                // 忽略MTU设置错误
            }
        }
    }
    
    /**
     * 添加数据传输回调
     */
    fun addDataCallback(callback: BleDataCallback) {
        if (!dataCallbacks.contains(callback)) {
            dataCallbacks.add(callback)
        }
    }
    
    /**
     * 移除数据传输回调
     */
    fun removeDataCallback(callback: BleDataCallback) {
        dataCallbacks.remove(callback)
    }
    
    /**
     * 移除所有数据传输回调
     */
    fun removeAllDataCallbacks() {
        dataCallbacks.clear()
    }
    
    /**
     * 清除所有通知状态
     */
    fun clearNotificationStates() {
        notificationStates.clear()
    }
    
    /**
     * 清除指定设备的通知状态
     * @param deviceInfo 设备信息
     */
    fun clearNotificationStates(deviceInfo: BleDeviceInfo) {
        val devicePrefix = "${deviceInfo.address}_"
        val keysToRemove = notificationStates.keys.filter { it.startsWith(devicePrefix) }
        keysToRemove.forEach { notificationStates.remove(it) }
    }
    
    // ==================== 回调通知方法 ====================
    
    private fun notifyOnWriteSuccess(
        deviceInfo: BleDeviceInfo,
        serviceUuid: String,
        characteristicUuid: String,
        data: ByteArray
    ) {
        dataCallbacks.forEach { callback ->
            try {
                callback.onWriteSuccess(deviceInfo, serviceUuid, characteristicUuid, data)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
    
    private fun notifyOnWriteFailed(
        deviceInfo: BleDeviceInfo,
        serviceUuid: String,
        characteristicUuid: String,
        errorCode: Int,
        errorMessage: String
    ) {
        dataCallbacks.forEach { callback ->
            try {
                callback.onWriteFailed(deviceInfo, serviceUuid, characteristicUuid, errorCode, errorMessage)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
    
    private fun notifyOnReadSuccess(
        deviceInfo: BleDeviceInfo,
        serviceUuid: String,
        characteristicUuid: String,
        data: ByteArray
    ) {
        dataCallbacks.forEach { callback ->
            try {
                callback.onReadSuccess(deviceInfo, serviceUuid, characteristicUuid, data)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
    
    private fun notifyOnReadFailed(
        deviceInfo: BleDeviceInfo,
        serviceUuid: String,
        characteristicUuid: String,
        errorCode: Int,
        errorMessage: String
    ) {
        dataCallbacks.forEach { callback ->
            try {
                callback.onReadFailed(deviceInfo, serviceUuid, characteristicUuid, errorCode, errorMessage)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
    
    private fun notifyOnNotificationReceived(
        deviceInfo: BleDeviceInfo,
        serviceUuid: String,
        characteristicUuid: String,
        data: ByteArray
    ) {
        dataCallbacks.forEach { callback ->
            try {
                callback.onNotificationReceived(deviceInfo, serviceUuid, characteristicUuid, data)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
    
    private fun notifyOnNotificationSetSuccess(
        deviceInfo: BleDeviceInfo,
        serviceUuid: String,
        characteristicUuid: String,
        isEnabled: Boolean
    ) {
        dataCallbacks.forEach { callback ->
            try {
                callback.onNotificationSetSuccess(deviceInfo, serviceUuid, characteristicUuid, isEnabled)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
    
    private fun notifyOnNotificationSetFailed(
        deviceInfo: BleDeviceInfo,
        serviceUuid: String,
        characteristicUuid: String,
        errorCode: Int,
        errorMessage: String
    ) {
        dataCallbacks.forEach { callback ->
            try {
                callback.onNotificationSetFailed(deviceInfo, serviceUuid, characteristicUuid, errorCode, errorMessage)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
}