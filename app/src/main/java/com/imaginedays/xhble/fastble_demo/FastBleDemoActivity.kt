package com.imaginedays.xhble.fastble_demo

import android.Manifest
import android.bluetooth.BluetoothAdapter
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Color
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.LinearLayout
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.button.MaterialButton
import com.google.android.material.textview.MaterialTextView
import com.imaginedays.xhble.R
import com.imaginedays.xhble.fastble_demo.adapter.BleDeviceAdapter
import com.imaginedays.xhble.fastble_demo.callback.BleScanCallback
import com.imaginedays.xhble.fastble_demo.callback.BleConnectionCallback
import com.imaginedays.xhble.fastble_demo.callback.BleDataCallback
import com.imaginedays.xhble.fastble_demo.manager.FastBleManager
import com.imaginedays.xhble.fastble_demo.model.BleDeviceInfo
import com.imaginedays.xhble.fastble_demo.model.ConnectionState
import com.imaginedays.xhble.fastble_demo.version.BluetoothVersionDetector
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

/**
 * BLE功能演示Activity
 * 展示扫描、连接、数据传输等功能
 */
@AndroidEntryPoint
class FastBleDemoActivity : AppCompatActivity() {
    
    @Inject
    lateinit var fastBleManager: FastBleManager
    
    @Inject
    lateinit var bluetoothVersionDetector: BluetoothVersionDetector
    
    private lateinit var deviceAdapter: BleDeviceAdapter
    
    // UI组件
     private lateinit var tvStatus: MaterialTextView
     private lateinit var layoutLocalBluetoothInfo: LinearLayout
     private lateinit var tvLocalBluetoothVersion: MaterialTextView
     private lateinit var tvLocalSupportedFeatures: MaterialTextView
     private lateinit var btnScan: MaterialButton
     private lateinit var btnStopScan: MaterialButton
     private lateinit var btnDisconnectAll: MaterialButton
     private lateinit var rvDevices: RecyclerView
    
    // 权限请求
    private val requestPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        val allGranted = permissions.values.all { it }
        if (allGranted) {
            initBluetooth()
        } else {
            showToast("需要蓝牙权限才能使用此功能")
            finish()
        }
    }
    
    // 蓝牙启用请求
    private val enableBluetoothLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == RESULT_OK) {
            initFastBle()
        } else {
            showToast("需要启用蓝牙才能使用此功能")
            finish()
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_fastble_demo)
         // 设置状态栏
        window.apply {
            statusBarColor = Color.TRANSPARENT
            decorView.systemUiVisibility = View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN or
                                         View.SYSTEM_UI_FLAG_LAYOUT_STABLE or
                                         View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR
        }
        
        initViews()
        checkPermissions()
    }
    
    private fun initViews() {
         tvStatus = findViewById(R.id.tv_status)
         layoutLocalBluetoothInfo = findViewById(R.id.layout_local_bluetooth_info)
         tvLocalBluetoothVersion = findViewById(R.id.tv_local_bluetooth_version)
         tvLocalSupportedFeatures = findViewById(R.id.tv_local_supported_features)
         btnScan = findViewById(R.id.btn_scan)
         btnStopScan = findViewById(R.id.btn_stop_scan)
         btnDisconnectAll = findViewById(R.id.btn_disconnect_all)
         rvDevices = findViewById(R.id.rv_devices)
        
        // 显示蓝牙版本信息
        displayBluetoothVersionInfo()
        
        // 设置RecyclerView
        deviceAdapter = BleDeviceAdapter { deviceInfo ->
            onDeviceItemClick(deviceInfo)
        }
        rvDevices.layoutManager = LinearLayoutManager(this)
        rvDevices.adapter = deviceAdapter
        
        // 设置操作按钮点击事件
        deviceAdapter.setOnActionClickListener { device ->
            // 跳转到设备服务列表页面
            val intent = Intent(this, DeviceServicesActivity::class.java).apply {
                putExtra(DeviceServicesActivity.EXTRA_DEVICE_NAME, device.name ?: "未知设备")
                putExtra(DeviceServicesActivity.EXTRA_DEVICE_MAC, device.address)
            }
            startActivity(intent)
        }
        
        // 设置按钮点击事件
        btnScan.setOnClickListener { startScan() }
        btnStopScan.setOnClickListener { stopScan() }
        btnDisconnectAll.setOnClickListener { disconnectAll() }
        
        updateUI()
    }
    
    private fun displayBluetoothVersionInfo() {
          val versionInfo = bluetoothVersionDetector.getLocalBluetoothVersion()
          
          // 显示蓝牙版本信息
          tvLocalBluetoothVersion.text = "蓝牙 ${versionInfo.bluetoothVersion} / BLE ${versionInfo.bleVersion}"
          tvLocalSupportedFeatures.text = "支持: ${versionInfo.supportedFeatures.joinToString(", ")}"
          
          // 显示本地蓝牙信息区域
          layoutLocalBluetoothInfo.visibility = View.VISIBLE
      }
    
    private fun checkPermissions() {
        val permissions = mutableListOf<String>()
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            permissions.addAll(
                listOf(
                    Manifest.permission.BLUETOOTH_SCAN,
                    Manifest.permission.BLUETOOTH_CONNECT,
                    Manifest.permission.ACCESS_FINE_LOCATION
                )
            )
        } else {
            permissions.addAll(
                listOf(
                    Manifest.permission.BLUETOOTH,
                    Manifest.permission.BLUETOOTH_ADMIN,
                    Manifest.permission.ACCESS_FINE_LOCATION
                )
            )
        }
        
        val needRequestPermissions = permissions.filter {
            ContextCompat.checkSelfPermission(this, it) != PackageManager.PERMISSION_GRANTED
        }
        
        if (needRequestPermissions.isNotEmpty()) {
            requestPermissionLauncher.launch(needRequestPermissions.toTypedArray())
        } else {
            initBluetooth()
        }
    }
    
    private fun initBluetooth() {
        val bluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
        if (bluetoothAdapter == null) {
            showToast("设备不支持蓝牙")
            finish()
            return
        }
        
        if (!bluetoothAdapter.isEnabled) {
            val enableBtIntent = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
            enableBluetoothLauncher.launch(enableBtIntent)
        } else {
            initFastBle()
        }
    }
    
    private fun initFastBle() {
        fastBleManager.init()
        
        // 添加回调监听
        fastBleManager.addScanCallback(scanCallback)
        fastBleManager.addConnectionCallback(connectionCallback)
        fastBleManager.addDataCallback(dataCallback)
        
        updateStatus("BLE初始化完成")
        updateUI()
    }
    
    private fun startScan() {
        if (!fastBleManager.isBluetoothEnabled()) {
            showToast("蓝牙未启用")
            return
        }
        
        deviceAdapter.clearDevices()
        fastBleManager.startScan(timeoutMillis = 10000)
        updateUI()
    }
    
    private fun stopScan() {
        fastBleManager.stopScan()
        updateUI()
    }
    
    private fun disconnectAll() {
        fastBleManager.disconnectAll()
        deviceAdapter.updateDevices(emptyList())
        updateStatus("已断开所有连接")
    }
    
    private fun onDeviceItemClick(deviceInfo: BleDeviceInfo) {
        val connectionState = fastBleManager.getConnectionState(deviceInfo)
        if (connectionState.isConnected()) {
            // 断开连接
            fastBleManager.disconnect(deviceInfo)
        } else if (!connectionState.isConnecting()) {
            // 连接设备
            fastBleManager.connect(deviceInfo)
        }
    }
    
    private fun updateUI() {
        val isScanning = if (::fastBleManager.isInitialized) {
            try {
                fastBleManager.isScanning()
            } catch (e: IllegalStateException) {
                // FastBleManager 未初始化
                false
            }
        } else {
            false
        }
        
        btnScan.isEnabled = !isScanning
        btnStopScan.isEnabled = isScanning
        
        if (isScanning) {
            updateStatus("正在扫描设备...")
        }
    }
    
    private fun updateStatus(message: String) {
        runOnUiThread {
            tvStatus.text = message
        }
    }
    
    private fun showToast(message: String) {
        runOnUiThread {
            Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
        }
    }
    
    // ==================== 回调实现 ====================
    
    private val scanCallback = object : BleScanCallback {
        override fun onScanStarted() {
            updateStatus("开始扫描设备")
            updateUI()
        }
        
        override fun onDeviceFound(deviceInfo: BleDeviceInfo) {
            runOnUiThread {
                deviceAdapter.addDevice(deviceInfo)
            }
        }
        
        override fun onScanFinished(scanResults: List<BleDeviceInfo>) {
            updateStatus("扫描完成，发现 ${scanResults.size} 个设备")
            runOnUiThread {
                deviceAdapter.updateDevices(scanResults)
                updateUI()
            }
        }
        
        override fun onScanFailed(errorCode: Int, errorMessage: String) {
            updateStatus("扫描失败: $errorMessage")
            updateUI()
        }
    }
    
    private val connectionCallback = object : BleConnectionCallback {
        override fun onConnectionStateChanged(deviceInfo: BleDeviceInfo, state: ConnectionState) {
            updateStatus("${deviceInfo.getDisplayName()} - ${state.getDescription()}")
            runOnUiThread {
                deviceAdapter.updateDevice(deviceInfo)
            }
        }
        
        override fun onConnectSuccess(deviceInfo: BleDeviceInfo) {
            updateStatus("${deviceInfo.getDisplayName()} 连接成功")
            
            // 连接成功后，可以进行数据操作示例
            // 这里可以根据具体设备的服务和特征值进行数据读写
        }
        
        override fun onConnectFailed(deviceInfo: BleDeviceInfo, errorCode: Int, errorMessage: String) {
            updateStatus("${deviceInfo.getDisplayName()} 连接失败: $errorMessage")
        }
        
        override fun onDisconnected(deviceInfo: BleDeviceInfo, isActiveDisconnect: Boolean) {
            val reason = if (isActiveDisconnect) "主动断开" else "意外断开"
            updateStatus("${deviceInfo.getDisplayName()} 已断开连接 ($reason)")
        }
        
        override fun onServicesDiscovered(deviceInfo: BleDeviceInfo, serviceUuids: List<String>) {
            updateStatus("${deviceInfo.getDisplayName()} 发现 ${serviceUuids.size} 个服务")
        }
    }
    
    private val dataCallback = object : BleDataCallback {
        override fun onWriteSuccess(
            deviceInfo: BleDeviceInfo,
            serviceUuid: String,
            characteristicUuid: String,
            data: ByteArray
        ) {
            updateStatus("数据写入成功: ${data.contentToString()}")
        }
        
        override fun onWriteFailed(
            deviceInfo: BleDeviceInfo,
            serviceUuid: String,
            characteristicUuid: String,
            errorCode: Int,
            errorMessage: String
        ) {
            updateStatus("数据写入失败: $errorMessage")
        }
        
        override fun onReadSuccess(
            deviceInfo: BleDeviceInfo,
            serviceUuid: String,
            characteristicUuid: String,
            data: ByteArray
        ) {
            updateStatus("数据读取成功: ${data.contentToString()}")
        }
        
        override fun onReadFailed(
            deviceInfo: BleDeviceInfo,
            serviceUuid: String,
            characteristicUuid: String,
            errorCode: Int,
            errorMessage: String
        ) {
            updateStatus("数据读取失败: $errorMessage")
        }
        
        override fun onNotificationReceived(
            deviceInfo: BleDeviceInfo,
            serviceUuid: String,
            characteristicUuid: String,
            data: ByteArray
        ) {
            updateStatus("收到通知数据: ${data.contentToString()}")
        }
        
        override fun onNotificationSetSuccess(
            deviceInfo: BleDeviceInfo,
            serviceUuid: String,
            characteristicUuid: String,
            isEnabled: Boolean
        ) {
            val action = if (isEnabled) "启用" else "停用"
            updateStatus("通知${action}成功")
        }
        
        override fun onNotificationSetFailed(
            deviceInfo: BleDeviceInfo,
            serviceUuid: String,
            characteristicUuid: String,
            errorCode: Int,
            errorMessage: String
        ) {
            updateStatus("通知设置失败: $errorMessage")
        }
    }
    
    override fun onDestroy() {
        super.onDestroy()
        if (::fastBleManager.isInitialized) {
            fastBleManager.removeScanCallback(scanCallback)
            fastBleManager.removeConnectionCallback(connectionCallback)
            fastBleManager.removeDataCallback(dataCallback)
            fastBleManager.stopScan()
            fastBleManager.disconnectAll()
        }
    }
}