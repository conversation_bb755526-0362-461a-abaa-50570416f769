package com.imaginedays.xhble.fastble_demo.model

import androidx.bluetooth.BluetoothDevice
import com.imaginedays.xhble.fastble_demo.version.BluetoothVersionInfo

/**
 * BLE设备信息数据类
 * 基于 AndroidX Bluetooth 的设备信息封装
 */
data class BleDeviceInfo(
    val device: BluetoothDevice,
    val name: String?,
    val address: String,
    val rssi: Int,
    val scanRecord: ByteArray? = null,
    val lastScanTime: Long = System.currentTimeMillis(),
    val bluetoothVersionInfo: BluetoothVersionInfo? = null
) {
    override fun equals(other: Any?): Bo<PERSON>an {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as BleDeviceInfo

        if (address != other.address) return false

        return true
    }

    override fun hashCode(): Int {
        return address.hashCode()
    }

    /**
     * 获取显示名称，优先使用设备名称，否则使用MAC地址
     */
    fun getDisplayName(): String {
        return if (!name.isNullOrBlank()) {
            name
        } else {
            address
        }
    }

    /**
     * 获取信号强度描述
     */
    fun getSignalStrengthDescription(): String {
        return when {
            rssi >= -50 -> "强"
            rssi >= -70 -> "中"
            rssi >= -90 -> "弱"
            else -> "很弱"
        }
    }
    
    /**
     * 获取蓝牙版本描述
     */
    fun getBluetoothVersionDescription(): String {
        return bluetoothVersionInfo?.let { versionInfo ->
            "蓝牙 ${versionInfo.bluetoothVersion} / BLE ${versionInfo.bleVersion}"
        } ?: "版本未知"
    }
    
    /**
     * 获取支持的功能特性描述
     */
    fun getSupportedFeaturesDescription(): String {
        return bluetoothVersionInfo?.supportedFeatures?.joinToString(", ") ?: "功能未知"
    }
    
    /**
     * 获取硬件版本信息
     */
    fun getHardwareVersionDescription(): String {
        return bluetoothVersionInfo?.hardwareVersion ?: "未知"
    }
    
    /**
     * 获取固件版本信息
     */
    fun getFirmwareVersionDescription(): String {
        return bluetoothVersionInfo?.firmwareVersion ?: "未知"
    }
    
    /**
     * 创建包含版本信息的副本
     */
    fun withVersionInfo(versionInfo: BluetoothVersionInfo?): BleDeviceInfo {
        return copy(bluetoothVersionInfo = versionInfo)
    }
}