package com.imaginedays.xhble.fastble_demo.ble

import androidx.bluetooth.ScanResult
import kotlinx.coroutines.flow.Flow

/**
 * BLE设备扫描接口
 * 基于 AndroidX Bluetooth 的现代化扫描实现
 */
interface BleScanner {
    /**
     * 启动BLE设备扫描
     * @param filters 扫描过滤器列表
     * @param timeoutMs 扫描超时时间（毫秒）
     * @return 扫描结果的Flow
     */
    fun startScan(
        filters: List<ScanFilter> = emptyList(),
        timeoutMs: Long = 10000
    ): Flow<ScanResult>
    
    /**
     * 停止扫描
     */
    suspend fun stopScan()
    
    /**
     * 检查是否正在扫描
     * @return 是否正在扫描
     */
    fun isScanning(): Boolean
}

/**
 * 扫描过滤器
 */
data class ScanFilter(
    val deviceName: String? = null,
    val serviceUuid: String? = null,
    val manufacturerData: ByteArray? = null
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as ScanFilter

        if (deviceName != other.deviceName) return false
        if (serviceUuid != other.serviceUuid) return false
        if (manufacturerData != null) {
            if (other.manufacturerData == null) return false
            if (!manufacturerData.contentEquals(other.manufacturerData)) return false
        } else if (other.manufacturerData != null) return false

        return true
    }

    override fun hashCode(): Int {
        var result = deviceName?.hashCode() ?: 0
        result = 31 * result + (serviceUuid?.hashCode() ?: 0)
        result = 31 * result + (manufacturerData?.contentHashCode() ?: 0)
        return result
    }
}