package com.imaginedays.xhble.fastble_demo.scanner

import androidx.bluetooth.ScanResult
import com.imaginedays.xhble.fastble_demo.ble.BleScanner
import com.imaginedays.xhble.fastble_demo.ble.ScanFilter
import com.imaginedays.xhble.fastble_demo.callback.BleScanCallback as CustomBleScanCallback
import com.imaginedays.xhble.fastble_demo.model.BleDeviceInfo
import com.imaginedays.xhble.fastble_demo.version.BluetoothVersionDetector
import javax.inject.Singleton
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import java.util.concurrent.ConcurrentHashMap
import javax.inject.Inject

/**
 * BLE设备扫描器
 * 负责扫描周围的BLE设备
 */
@Singleton
class BleDeviceScanner @Inject constructor(
    private val bleScanner: BleScanner,
    private val bluetoothVersionDetector: BluetoothVersionDetector
) {
    
    private val scanCallbacks = mutableListOf<CustomBleScanCallback>()
    private val scanResults = ConcurrentHashMap<String, BleDeviceInfo>()
    private val coroutineScope = CoroutineScope(Dispatchers.Main)
    private var scanJob: Job? = null
    
    /**
     * 开始扫描设备
     * @param timeoutMillis 扫描超时时间（毫秒）
     * @param deviceNames 指定扫描的设备名称列表，为空则扫描所有设备
     * @param serviceUuids 指定扫描的服务UUID列表，为空则扫描所有服务
     */
    fun startScan(
        timeoutMillis: Long = 10000,
        deviceNames: Array<String>? = null,
        serviceUuids: Array<String>? = null
    ) {
        if (bleScanner.isScanning()) {
            return
        }
        
        // 清空之前的扫描结果
        scanResults.clear()
        
        // 配置扫描过滤器
        val filters = mutableListOf<ScanFilter>()
        
        // 设置设备名称过滤
        deviceNames?.forEach { name ->
            filters.add(ScanFilter(deviceName = name))
        }
        
        // 设置服务UUID过滤
        serviceUuids?.forEach { uuid ->
            filters.add(ScanFilter(serviceUuid = uuid))
        }
        
        // 如果没有过滤器，添加一个空过滤器以扫描所有设备
        if (filters.isEmpty()) {
            filters.add(ScanFilter())
        }
        
        // 开始扫描
        scanJob = coroutineScope.launch {
            try {
                notifyOnScanStarted()
                
                bleScanner.startScan(
                    filters = filters
                ).onEach { scanResult ->
                    handleScanResult(scanResult)
                }.launchIn(this)
                
            } catch (e: Exception) {
                notifyOnScanFailed(-1, "扫描启动失败: ${e.message}")
            }
        }
    }

    private fun handleScanResult(scanResult: ScanResult) {
        val device = scanResult.device
        val deviceName = device.name
        
        // 只显示以"Hi-XH"开头的设备名称
        deviceName?.let { name ->
            // if (isXHDevice(name)) {
                coroutineScope.launch {
                    val deviceInfo = createBleDeviceInfo(scanResult)
                    
                    // 异步获取设备版本信息
                    val versionInfo = try {
                        bluetoothVersionDetector.getRemoteDeviceVersion(device)
                    } catch (e: Exception) {
                        null
                    }
                    
                    // 创建包含版本信息的设备信息
                    val deviceInfoWithVersion = deviceInfo.withVersionInfo(versionInfo)
                    scanResults[deviceInfoWithVersion.address] = deviceInfoWithVersion
                    notifyOnDeviceFound(deviceInfoWithVersion)
                }
            // }
        }
    }
    
    fun isXHDevice(deviceName: String): Boolean {
        return deviceName.startsWith("Hi-XH")
    }
    
    /**
     * 停止扫描
     */
    fun stopScan() {
        scanJob?.cancel()
        scanJob = null
        coroutineScope.launch {
            bleScanner.stopScan()
        }
        
        // 通知扫描结束
        val results = scanResults.values.toList()
        notifyOnScanFinished(results)
    }
    
    /**
     * 是否正在扫描
     */
    fun isScanning(): Boolean {
        return bleScanner.isScanning()
    }
    
    /**
     * 获取扫描结果
     */
    fun getScanResults(): List<BleDeviceInfo> {
        return scanResults.values.toList()
    }
    
    /**
     * 根据MAC地址获取设备信息
     */
    fun getDeviceByMac(macAddress: String): BleDeviceInfo? {
        return scanResults[macAddress]
    }
    
    /**
     * 清空扫描结果
     */
    fun clearScanResults() {
        scanResults.clear()
    }
    
    /**
     * 添加扫描回调
     */
    fun addScanCallback(callback: CustomBleScanCallback) {
        if (!scanCallbacks.contains(callback)) {
            scanCallbacks.add(callback)
        }
    }
    
    /**
     * 移除扫描回调
     */
    fun removeScanCallback(callback: CustomBleScanCallback) {
        scanCallbacks.remove(callback)
    }
    
    /**
     * 移除所有扫描回调
     */
    fun removeAllScanCallbacks() {
        scanCallbacks.clear()
    }
    
    /**
     * 创建BleDeviceInfo对象
     */
    private fun createBleDeviceInfo(scanResult: ScanResult): BleDeviceInfo {
        return BleDeviceInfo(
            name = scanResult.device.name ?: "Unknown",
            address = getDeviceAddress(scanResult.device),
            device = scanResult.device,
            rssi = scanResult.rssi,
            lastScanTime = System.currentTimeMillis()
        )
    }
    
    /**
     * 获取本地设备的蓝牙版本信息
     */
    fun getLocalBluetoothVersion() = bluetoothVersionDetector.getLocalBluetoothVersion()
    
    /**
     * 为已扫描的设备异步获取版本信息
     */
    suspend fun updateDeviceVersionInfo(deviceAddress: String) {
        val deviceInfo = scanResults[deviceAddress] ?: return
        
        try {
            val versionInfo = bluetoothVersionDetector.getRemoteDeviceVersion(deviceInfo.device)
            val updatedDeviceInfo = deviceInfo.withVersionInfo(versionInfo)
            scanResults[deviceAddress] = updatedDeviceInfo
            notifyOnDeviceFound(updatedDeviceInfo)
        } catch (e: Exception) {
            // 版本信息获取失败，保持原有设备信息
        }
    }
    
    /**
     * 获取设备地址
     * 使用反射访问 AndroidX BluetoothDevice 的内部 fwkDevice 字段来获取地址
     */
    private fun getDeviceAddress(device: androidx.bluetooth.BluetoothDevice): String {
        return try {
            // 使用反射访问内部的 fwkDevice 字段
            val fwkDeviceField = device.javaClass.getDeclaredField("fwkDevice")
            fwkDeviceField.isAccessible = true
            val fwkDevice = fwkDeviceField.get(device) as android.bluetooth.BluetoothDevice
            fwkDevice.address
        } catch (e: Exception) {
            // 如果反射失败，使用设备的 toString() 方法尝试提取地址
            // AndroidX BluetoothDevice 的 toString() 通常包含地址信息
            val deviceString = device.toString()
            // 尝试从字符串中提取 MAC 地址格式 (XX:XX:XX:XX:XX:XX)
            val macRegex = "([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})".toRegex()
            macRegex.find(deviceString)?.value ?: "Unknown"
        }
    }
    
    // ==================== 回调通知方法 ====================
    
    private fun notifyOnScanStarted() {
        scanCallbacks.forEach { callback ->
            try {
                callback.onScanStarted()
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
    
    private fun notifyOnDeviceFound(deviceInfo: BleDeviceInfo) {
        scanCallbacks.forEach { callback ->
            try {
                callback.onDeviceFound(deviceInfo)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
    
    private fun notifyOnScanFinished(scanResults: List<BleDeviceInfo>) {
        scanCallbacks.forEach { callback ->
            try {
                callback.onScanFinished(scanResults)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
    
    private fun notifyOnScanFailed(errorCode: Int, errorMessage: String) {
        scanCallbacks.forEach { callback ->
            try {
                callback.onScanFailed(errorCode, errorMessage)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
}