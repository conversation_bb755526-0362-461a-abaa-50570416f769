package com.imaginedays.xhble.fastble_demo.di

import android.content.Context
import com.imaginedays.xhble.fastble_demo.ble.BleConnectionManager
import com.imaginedays.xhble.fastble_demo.ble.BleConnectionManagerImpl
import com.imaginedays.xhble.fastble_demo.ble.BleDataTransfer
import com.imaginedays.xhble.fastble_demo.ble.BleDataTransferImpl
import com.imaginedays.xhble.fastble_demo.ble.BleScanner
import com.imaginedays.xhble.fastble_demo.ble.BleScannerImpl
import com.imaginedays.xhble.fastble_demo.version.BluetoothVersionDetector
import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import javax.inject.Qualifier
import javax.inject.Singleton

@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class ApplicationScope

@Module
@InstallIn(SingletonComponent::class)
abstract class BleModule {
    
    @Binds
    @Singleton
    abstract fun bindBleScanner(impl: BleScannerImpl): BleScanner
    
    @Binds
    @Singleton
    abstract fun bindBleConnectionManager(impl: BleConnectionManagerImpl): BleConnectionManager
    
    @Binds
    @Singleton
    abstract fun bindBleDataTransfer(impl: BleDataTransferImpl): BleDataTransfer
    
    companion object {
        
        @Provides
        @Singleton
        @ApplicationScope
        fun provideApplicationScope(): CoroutineScope {
            return CoroutineScope(SupervisorJob() + Dispatchers.Main)
        }
        
        @Provides
        @Singleton
        fun provideBluetoothVersionDetector(
            @ApplicationContext context: Context
        ): BluetoothVersionDetector {
            return BluetoothVersionDetector(context)
        }
    }
}