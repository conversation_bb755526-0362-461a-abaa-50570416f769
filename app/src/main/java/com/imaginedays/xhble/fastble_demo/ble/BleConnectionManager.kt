package com.imaginedays.xhble.fastble_demo.ble

import androidx.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothGattService
import androidx.bluetooth.GattClientScope
import kotlinx.coroutines.flow.Flow

/**
 * 连接状态枚举
 */
enum class ConnectionState {
    DISCONNECTED,
    CONNECTING,
    CONNECTED,
    DISCONNECTING,
    ERROR
}

/**
 * GATT连接封装
 */
data class GattConnection(
    val device: BluetoothDevice,
    val services: List<BluetoothGattService>,
    val connectionState: ConnectionState,
    internal val gattScope: GattClientScope
)

/**
 * BLE连接管理接口
 * 基于 AndroidX Bluetooth 的连接管理
 */
interface BleConnectionManager {
    /**
     * 连接到BLE设备
     * @param device 要连接的设备
     * @param autoConnect 是否自动连接
     * @return 连接结果
     */
    suspend fun connect(
        device: BluetoothDevice,
        autoConnect: Boolean = false
    ): Result<GattConnection>
    
    /**
     * 断开设备连接
     * @param device 要断开的设备
     */
    suspend fun disconnect(device: BluetoothDevice)
    
    /**
     * 获取设备连接状态
     * @param device 设备
     * @return 连接状态
     */
    fun getConnectionState(device: BluetoothDevice): ConnectionState
    
    /**
     * 获取已连接设备列表
     * @return 已连接设备的Flow
     */
    fun getConnectedDevices(): Flow<List<BluetoothDevice>>
    
    /**
     * 获取指定设备的GATT连接
     * @param device 设备
     * @return GATT连接，如果未连接则返回null
     */
    fun getGattConnection(device: BluetoothDevice): GattConnection?
}