package com.imaginedays.xhble.fastble_demo.ble

import android.annotation.SuppressLint
import android.content.Context
import androidx.bluetooth.BluetoothLe
import androidx.bluetooth.ScanResult
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Singleton

/**
 * BLE扫描器实现
 * 基于 AndroidX Bluetooth 的扫描功能
 */
@Singleton
class BleScannerImpl @Inject constructor(
    @ApplicationContext private val context: Context
) : BleScanner {
    
    private val bluetoothLe = BluetoothLe(context)
    private val scanScope = CoroutineScope(Dispatchers.IO)
    private var scanJob: Job? = null
    private var timeoutJob: Job? = null
    
    private val _scanResults = MutableSharedFlow<ScanResult>()
    private val scanResults = _scanResults.asSharedFlow()
    
    @Volatile
    private var isCurrentlyScanning = false
    
    @SuppressLint("MissingPermission")
    override fun startScan(
        filters: List<ScanFilter>,
        timeoutMs: Long
    ): Flow<ScanResult> {
        if (isCurrentlyScanning) {
            return scanResults
        }
        
        isCurrentlyScanning = true
        
        scanJob = scanScope.launch {
            try {
                // 使用 AndroidX Bluetooth 进行扫描
                bluetoothLe.scan().collect { scanResult ->
                    // 应用过滤器
                    if (shouldIncludeResult(scanResult, filters)) {
                        _scanResults.emit(scanResult)
                    }
                }
            } catch (e: Exception) {
                // 处理扫描异常
                isCurrentlyScanning = false
            }
        }
        
        // 设置扫描超时
        if (timeoutMs > 0) {
            timeoutJob = scanScope.launch {
                delay(timeoutMs)
                stopScan()
            }
        }
        
        return scanResults
    }
    
    override suspend fun stopScan() {
        isCurrentlyScanning = false
        scanJob?.cancel()
        timeoutJob?.cancel()
        scanJob = null
        timeoutJob = null
    }
    
    override fun isScanning(): Boolean {
        return isCurrentlyScanning
    }
    
    /**
     * 检查扫描结果是否应该包含在结果中
     */
    private fun shouldIncludeResult(
        scanResult: ScanResult,
        filters: List<ScanFilter>
    ): Boolean {
        if (filters.isEmpty()) {
            return true
        }
        
        return filters.any { filter ->
            var matches = true
            
            // 检查设备名称过滤器
            filter.deviceName?.let { filterName ->
                val deviceName = scanResult.device.name
                matches = matches && deviceName?.contains(filterName, ignoreCase = true) == true
            }
            
            // 检查服务UUID过滤器
            filter.serviceUuid?.let { filterUuid ->
                // 这里需要从扫描记录中解析服务UUID
                // 简化实现，实际项目中需要解析广播数据
                matches = matches && true // 暂时总是匹配
            }
            
            // 检查制造商数据过滤器
            filter.manufacturerData?.let { filterData ->
                // 这里需要从扫描记录中解析制造商数据
                // 简化实现，实际项目中需要解析广播数据
                matches = matches && true // 暂时总是匹配
            }
            
            matches
        }
    }
}