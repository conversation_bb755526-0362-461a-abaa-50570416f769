package com.imaginedays.xhble.fastble_demo.ble

import kotlinx.coroutines.flow.Flow
import java.util.UUID

/**
 * BLE数据传输接口
 * 基于 AndroidX Bluetooth 的数据读写和通知管理
 */
interface BleDataTransfer {
    /**
     * 读取特征值
     * @param connection GATT连接
     * @param serviceUuid 服务UUID
     * @param characteristicUuid 特征值UUID
     * @return 读取结果
     */
    suspend fun readCharacteristic(
        connection: GattConnection,
        serviceUuid: UUID,
        characteristicUuid: UUID
    ): Result<ByteArray>
    
    /**
     * 写入特征值
     * @param connection GATT连接
     * @param serviceUuid 服务UUID
     * @param characteristicUuid 特征值UUID
     * @param data 要写入的数据
     * @return 写入结果
     */
    suspend fun writeCharacteristic(
        connection: GattConnection,
        serviceUuid: UUID,
        characteristicUuid: UUID,
        data: ByteArray
    ): Result<Unit>
    
    /**
     * 订阅特征值通知
     * @param connection GATT连接
     * @param serviceUuid 服务UUID
     * @param characteristicUuid 特征值UUID
     * @return 通知数据的Flow
     */
    fun subscribeToNotifications(
        connection: GattConnection,
        serviceUuid: UUID,
        characteristicUuid: UUID
    ): Flow<ByteArray>
    
    /**
     * 取消特征值通知订阅
     * @param connection GATT连接
     * @param serviceUuid 服务UUID
     * @param characteristicUuid 特征值UUID
     */
    suspend fun unsubscribeFromNotifications(
        connection: GattConnection,
        serviceUuid: UUID,
        characteristicUuid: UUID
    ): Result<Unit>
    
    /**
     * 设置MTU大小
     * @param connection GATT连接
     * @param mtu MTU大小
     * @return 设置结果
     */
    suspend fun setMtu(
        connection: GattConnection,
        mtu: Int
    ): Result<Int>
}