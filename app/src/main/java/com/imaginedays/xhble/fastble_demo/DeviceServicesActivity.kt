package com.imaginedays.xhble.fastble_demo

import android.os.Bundle
import android.view.MenuItem
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.imaginedays.xhble.R
import com.imaginedays.xhble.fastble_demo.adapter.BleServiceAdapter
import com.imaginedays.xhble.fastble_demo.manager.FastBleManager
import com.imaginedays.xhble.fastble_demo.model.BleDeviceInfo
import com.imaginedays.xhble.fastble_demo.callback.BleConnectionCallback
import com.imaginedays.xhble.fastble_demo.callback.BleDataCallback
import com.imaginedays.xhble.fastble_demo.model.ConnectionState
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class DeviceServicesActivity : AppCompatActivity() {

    companion object {
        const val EXTRA_DEVICE_NAME = "device_name"
        const val EXTRA_DEVICE_MAC = "device_mac"
    }

    @Inject
    lateinit var fastBleManager: FastBleManager
    
    private lateinit var tvDeviceName: TextView
    private lateinit var tvDeviceMac: TextView
    private lateinit var recyclerViewServices: RecyclerView
    private lateinit var serviceAdapter: BleServiceAdapter

    private var deviceName: String? = null
    private var deviceMac: String? = null
    private var deviceInfo: BleDeviceInfo? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_device_services)

        // 设置ActionBar
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            title = "设备服务列表"
        }

        initViews()
        initData()
        setupCallbacks()
    }

    private fun initViews() {
        tvDeviceName = findViewById(R.id.tv_device_name)
        tvDeviceMac = findViewById(R.id.tv_device_mac)
        recyclerViewServices = findViewById(R.id.recycler_view_services)

        // 设置RecyclerView
        serviceAdapter = BleServiceAdapter(this)
        recyclerViewServices.apply {
            layoutManager = LinearLayoutManager(this@DeviceServicesActivity)
            adapter = serviceAdapter
        }
        
        // 设置特征点击监听器
        serviceAdapter.setOnCharacteristicClickListener { service, characteristic ->
            readCharacteristic(service, characteristic)
        }
    }

    private fun initData() {
        deviceName = intent.getStringExtra(EXTRA_DEVICE_NAME)
        deviceMac = intent.getStringExtra(EXTRA_DEVICE_MAC)

        tvDeviceName.text = deviceName ?: "未知设备"
        tvDeviceMac.text = "MAC: ${deviceMac ?: "未知"}"

        // 查找对应的设备信息
        deviceMac?.let { mac ->
            deviceInfo = fastBleManager.getConnectedDevices().find { it.address == mac }
            // 加载设备服务
            loadDeviceServices()
        }
    }
    
    private fun setupCallbacks() {
        fastBleManager.addConnectionCallback(connectionCallback)
        fastBleManager.addDataCallback(dataCallback)
    }
    
    private val connectionCallback = object : BleConnectionCallback {
        override fun onConnectionStateChanged(deviceInfo: BleDeviceInfo, state: ConnectionState) {
            if (deviceInfo.address == deviceMac) {
                runOnUiThread {
                    when (state) {
                        ConnectionState.DISCONNECTED -> {
                            Toast.makeText(this@DeviceServicesActivity, "设备已断开连接", Toast.LENGTH_SHORT).show()
                            finish()
                        }
                        else -> {}
                    }
                }
            }
        }
        
        override fun onConnectSuccess(deviceInfo: BleDeviceInfo) {}
        override fun onConnectFailed(deviceInfo: BleDeviceInfo, errorCode: Int, errorMessage: String) {}
        override fun onDisconnected(deviceInfo: BleDeviceInfo, isActiveDisconnect: Boolean) {}
        override fun onServicesDiscovered(deviceInfo: BleDeviceInfo, serviceUuids: List<String>) {
            if (deviceInfo.address == deviceMac) {
                runOnUiThread {
                    // 更新服务列表
                    Toast.makeText(this@DeviceServicesActivity, "发现 ${serviceUuids.size} 个服务", Toast.LENGTH_SHORT).show()
                }
            }
        }
    }
    
    private val dataCallback = object : BleDataCallback {
        override fun onWriteSuccess(deviceInfo: BleDeviceInfo, serviceUuid: String, characteristicUuid: String, data: ByteArray) {
            runOnUiThread {
                Toast.makeText(this@DeviceServicesActivity, "数据写入成功", Toast.LENGTH_SHORT).show()
            }
        }
        
        override fun onWriteFailed(deviceInfo: BleDeviceInfo, serviceUuid: String, characteristicUuid: String, errorCode: Int, errorMessage: String) {
            runOnUiThread {
                Toast.makeText(this@DeviceServicesActivity, "数据写入失败: $errorMessage", Toast.LENGTH_SHORT).show()
            }
        }
        
        override fun onReadSuccess(deviceInfo: BleDeviceInfo, serviceUuid: String, characteristicUuid: String, data: ByteArray) {
            if (deviceInfo.address == deviceMac) {
                runOnUiThread {
                    // 将字节数组转换为十六进制字符串显示
                    val hexValue = data.joinToString(" ") { "%02X".format(it) }
                    val displayValue = if (data.isNotEmpty()) {
                        "0x$hexValue (${data.size} bytes)"
                    } else {
                        "空值"
                    }
                    
                    android.util.Log.d("imaginedays", "[DEVICE_SERVICES] 特征读取成功: $characteristicUuid, 值: $displayValue")
                    
                    // 更新适配器中的特征值显示
                    serviceAdapter.updateCharacteristicValue(serviceUuid, characteristicUuid, data)
                    
                    Toast.makeText(this@DeviceServicesActivity, "特征值读取成功: $displayValue", Toast.LENGTH_LONG).show()
                }
            }
        }
        
        override fun onReadFailed(deviceInfo: BleDeviceInfo, serviceUuid: String, characteristicUuid: String, errorCode: Int, errorMessage: String) {
            if (deviceInfo.address == deviceMac) {
                runOnUiThread {
                    android.util.Log.e("imaginedays", "[DEVICE_SERVICES] 特征读取失败: $characteristicUuid, 错误: $errorMessage")
                    Toast.makeText(this@DeviceServicesActivity, "特征值读取失败: $errorMessage", Toast.LENGTH_LONG).show()
                }
            }
        }
        
        override fun onNotificationReceived(deviceInfo: BleDeviceInfo, serviceUuid: String, characteristicUuid: String, data: ByteArray) {
            runOnUiThread {
                Toast.makeText(this@DeviceServicesActivity, "收到通知数据", Toast.LENGTH_SHORT).show()
            }
        }
        
        override fun onNotificationSetSuccess(deviceInfo: BleDeviceInfo, serviceUuid: String, characteristicUuid: String, isEnabled: Boolean) {
            runOnUiThread {
                val action = if (isEnabled) "启用" else "停用"
                Toast.makeText(this@DeviceServicesActivity, "通知${action}成功", Toast.LENGTH_SHORT).show()
            }
        }
        
        override fun onNotificationSetFailed(deviceInfo: BleDeviceInfo, serviceUuid: String, characteristicUuid: String, errorCode: Int, errorMessage: String) {
            runOnUiThread {
                Toast.makeText(this@DeviceServicesActivity, "通知设置失败: $errorMessage", Toast.LENGTH_SHORT).show()
            }
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                finish()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }
    
    /**
     * 加载设备服务
     */
    private fun loadDeviceServices() {
        deviceInfo?.let { device ->
            try {
                // 通过反射获取连接管理器
                val fastBleManagerClass = fastBleManager.javaClass
                val connectionManagerField = fastBleManagerClass.getDeclaredField("connectionManager")
                connectionManagerField.isAccessible = true
                val connectionManager = connectionManagerField.get(fastBleManager)
                
                // 获取GattConnection
                val getGattConnectionMethod = connectionManager.javaClass.getMethod("getGattConnection", androidx.bluetooth.BluetoothDevice::class.java)
                val gattConnection = getGattConnectionMethod.invoke(connectionManager, device.device)
                
                if (gattConnection != null) {
                    // 获取services字段
                    val servicesField = gattConnection.javaClass.getDeclaredField("services")
                    servicesField.isAccessible = true
                    val services = servicesField.get(gattConnection) as List<android.bluetooth.BluetoothGattService>
                    
                    if (services.isNotEmpty()) {
                        serviceAdapter.updateServices(services)
                        android.util.Log.d("imaginedays", "[DEVICE_SERVICES] 加载了 ${services.size} 个服务")
                    } else {
                        Toast.makeText(this, "未发现设备服务，请确保设备已连接并完成服务发现", Toast.LENGTH_SHORT).show()
                    }
                } else {
                    Toast.makeText(this, "设备未连接，无法获取服务列表", Toast.LENGTH_SHORT).show()
                }
            } catch (e: Exception) {
                android.util.Log.e("imaginedays", "[DEVICE_SERVICES] 获取服务列表失败: ${e.message}")
                Toast.makeText(this, "获取服务列表失败: ${e.message}", Toast.LENGTH_SHORT).show()
            }
        }
    }
    
    /**
     * 读取特征值
     */
    private fun readCharacteristic(service: android.bluetooth.BluetoothGattService, characteristic: android.bluetooth.BluetoothGattCharacteristic) {
        deviceInfo?.let { device ->
            android.util.Log.d("imaginedays", "[DEVICE_SERVICES] 开始读取特征: ${characteristic.uuid}")
            Toast.makeText(this, "正在读取特征值...", Toast.LENGTH_SHORT).show()
            
            fastBleManager.readData(
                device,
                service.uuid.toString(),
                characteristic.uuid.toString()
            )
        } ?: run {
            Toast.makeText(this, "设备信息不可用", Toast.LENGTH_SHORT).show()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        fastBleManager.removeConnectionCallback(connectionCallback)
        fastBleManager.removeDataCallback(dataCallback)
    }
}
