package com.imaginedays.xhble.fastble_demo.ble

import android.bluetooth.BluetoothGattCharacteristic
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import java.util.UUID
import java.util.concurrent.ConcurrentHashMap
import javax.inject.Inject
import javax.inject.Singleton

/**
 * BLE数据传输实现
 * 基于 AndroidX Bluetooth 的数据读写和通知管理
 */
@Singleton
class BleDataTransferImpl @Inject constructor() : BleDataTransfer {
    
    private val operationMutex = Mutex()
    private val notificationScope = CoroutineScope(Dispatchers.IO)
    
    // 通知数据流管理
    private val _notificationFlow = MutableSharedFlow<NotificationData>()
    private val notificationFlow = _notificationFlow.asSharedFlow()
    
    // 存储活跃的通知订阅
    private val activeNotifications = ConcurrentHashMap<String, Boolean>()
    
    override suspend fun readCharacteristic(
        connection: GattConnection,
        serviceUuid: UUID,
        characteristicUuid: UUID
    ): Result<ByteArray> {
        return operationMutex.withLock {
            try {
                val characteristic = findCharacteristic(connection, serviceUuid, characteristicUuid)
                    ?: return@withLock Result.failure(IllegalArgumentException("Characteristic not found"))
                
                // 使用 GattClientScope 读取特征值
                // 注意：AndroidX Bluetooth 的 API 可能需要调整
                // 这里提供基本框架，实际实现需要根据最新的 AndroidX Bluetooth API
                val data = ByteArray(0) // 临时实现
                Result.success(data)
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }
    
    override suspend fun writeCharacteristic(
        connection: GattConnection,
        serviceUuid: UUID,
        characteristicUuid: UUID,
        data: ByteArray
    ): Result<Unit> {
        return operationMutex.withLock {
            try {
                val characteristic = findCharacteristic(connection, serviceUuid, characteristicUuid)
                    ?: return@withLock Result.failure(IllegalArgumentException("Characteristic not found"))
                
                // 使用 GattClientScope 写入特征值
                // 注意：AndroidX Bluetooth 的 API 可能需要调整
                // 这里提供基本框架，实际实现需要根据最新的 AndroidX Bluetooth API
                Result.success(Unit)
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }
    
    override fun subscribeToNotifications(
        connection: GattConnection,
        serviceUuid: UUID,
        characteristicUuid: UUID
    ): Flow<ByteArray> {
        val notificationKey = getNotificationKey(getDeviceAddress(connection.device), serviceUuid, characteristicUuid)
        
        // 标记为活跃订阅
        activeNotifications[notificationKey] = true
        
        // 启动通知订阅
        notificationScope.launch {
            try {
                val characteristic = findCharacteristic(connection, serviceUuid, characteristicUuid)
                if (characteristic != null) {
                    // 使用 GattClientScope 订阅通知
                    // 注意：AndroidX Bluetooth 的通知订阅 API 可能需要调整
                    // 这里提供基本框架
                    
                    // 模拟通知数据发送
                    _notificationFlow.emit(
                        NotificationData(
                            deviceAddress = getDeviceAddress(connection.device),
                            serviceUuid = serviceUuid,
                            characteristicUuid = characteristicUuid,
                            data = ByteArray(0)
                        )
                    )
                }
            } catch (e: Exception) {
                // 处理订阅异常
            }
        }
        
        // 返回过滤后的通知流
        return notificationFlow
            .filter { notification ->
                notification.deviceAddress == getDeviceAddress(connection.device) &&
                notification.serviceUuid == serviceUuid &&
                notification.characteristicUuid == characteristicUuid
            }
            .filter { activeNotifications[notificationKey] == true }
            .map { it.data }
    }
    
    override suspend fun unsubscribeFromNotifications(
        connection: GattConnection,
        serviceUuid: UUID,
        characteristicUuid: UUID
    ): Result<Unit> {
        return try {
            val notificationKey = getNotificationKey(getDeviceAddress(connection.device), serviceUuid, characteristicUuid)
            
            // 移除活跃订阅标记
            activeNotifications.remove(notificationKey)
            
            // 这里应该调用 GattClientScope 的取消订阅方法
            // 但 AndroidX Bluetooth 的取消订阅通常通过取消协程来实现
            
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    override suspend fun setMtu(
        connection: GattConnection,
        mtu: Int
    ): Result<Int> {
        return try {
            // 使用 GattClientScope 设置 MTU
            // 注意：AndroidX Bluetooth 的 MTU 设置方法可能不同
            // 这里提供一个基本的实现框架
            
            // 验证 MTU 范围
            val validMtu = mtu.coerceIn(23, 517)
            
            // 实际的 MTU 设置需要根据 AndroidX Bluetooth 的具体 API
            // connection.gattScope.requestMtu(validMtu)
            
            Result.success(validMtu)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 查找指定的特征值
     */
    private fun findCharacteristic(
        connection: GattConnection,
        serviceUuid: UUID,
        characteristicUuid: UUID
    ): BluetoothGattCharacteristic? {
        return connection.services
            .find { it.uuid == serviceUuid }
            ?.getCharacteristic(characteristicUuid)
    }
    
    /**
     * 生成通知订阅的唯一键
     */
    private fun getNotificationKey(
        deviceAddress: String,
        serviceUuid: UUID,
        characteristicUuid: UUID
    ): String {
        return "${deviceAddress}_${serviceUuid}_${characteristicUuid}"
    }
    
    /**
     * 获取设备地址
     * 使用反射访问 AndroidX BluetoothDevice 的内部 fwkDevice 字段来获取地址
     */
    private fun getDeviceAddress(device: androidx.bluetooth.BluetoothDevice): String {
        return try {
            // 使用反射访问内部的 fwkDevice 字段
            val fwkDeviceField = device.javaClass.getDeclaredField("fwkDevice")
            fwkDeviceField.isAccessible = true
            val fwkDevice = fwkDeviceField.get(device) as android.bluetooth.BluetoothDevice
            fwkDevice.address
        } catch (e: Exception) {
            // 如果反射失败，使用设备的 toString() 方法尝试提取地址
            val deviceString = device.toString()
            // 尝试从字符串中提取 MAC 地址格式 (XX:XX:XX:XX:XX:XX)
            val macRegex = "([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})".toRegex()
            macRegex.find(deviceString)?.value ?: "Unknown"
        }
    }
}

/**
 * 通知数据封装
 */
private data class NotificationData(
    val deviceAddress: String,
    val serviceUuid: UUID,
    val characteristicUuid: UUID,
    val data: ByteArray
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as NotificationData

        if (deviceAddress != other.deviceAddress) return false
        if (serviceUuid != other.serviceUuid) return false
        if (characteristicUuid != other.characteristicUuid) return false
        if (!data.contentEquals(other.data)) return false

        return true
    }

    override fun hashCode(): Int {
        var result = deviceAddress.hashCode()
        result = 31 * result + serviceUuid.hashCode()
        result = 31 * result + characteristicUuid.hashCode()
        result = 31 * result + data.contentHashCode()
        return result
    }
}