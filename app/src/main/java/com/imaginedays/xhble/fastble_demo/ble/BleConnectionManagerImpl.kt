package com.imaginedays.xhble.fastble_demo.ble

import android.content.Context
import androidx.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothGattService
import androidx.bluetooth.BluetoothLe
import androidx.bluetooth.GattClientScope
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import java.util.concurrent.ConcurrentHashMap
import javax.inject.Inject
import javax.inject.Singleton

/**
 * BLE连接管理器实现
 * 基于 AndroidX Bluetooth 的连接管理
 */
@Singleton
class BleConnectionManagerImpl @Inject constructor(
    @ApplicationContext private val context: Context
) : BleConnectionManager {
    
    private val bluetoothLe = BluetoothLe(context)
    private val connectionScope = CoroutineScope(Dispatchers.IO)
    private val connectionMutex = Mutex()
    
    // 存储设备连接状态
    private val connectionStates = ConcurrentHashMap<String, ConnectionState>()
    private val gattConnections = ConcurrentHashMap<String, GattConnection>()
    
    // 连接状态流
    private val _connectedDevices = MutableStateFlow<List<BluetoothDevice>>(emptyList())
    private val connectedDevices = _connectedDevices.asStateFlow()
    
    override suspend fun connect(
        device: BluetoothDevice,
        autoConnect: Boolean
    ): Result<GattConnection> {
        return connectionMutex.withLock {
            try {
                val deviceAddress = getDeviceAddress(device)
                
                // 检查是否已经连接
                if (connectionStates[deviceAddress] == ConnectionState.CONNECTED) {
                    gattConnections[deviceAddress]?.let {
                        return@withLock Result.success(it)
                    }
                }
                
                // 更新连接状态为连接中
                updateConnectionState(deviceAddress, ConnectionState.CONNECTING)
                
                // 暂时使用模拟连接，因为 AndroidX Bluetooth 还在开发中
                // 实际项目中需要根据最新的 AndroidX Bluetooth API 进行调整
                
                // 创建模拟连接对象
                val connection = GattConnection(
                    device = device,
                    services = emptyList<BluetoothGattService>(),
                    connectionState = ConnectionState.CONNECTED,
                    gattScope = null as? GattClientScope ?: throw Exception("GattClientScope not available")
                )
                
                // 存储连接
                gattConnections[deviceAddress] = connection
                updateConnectionState(deviceAddress, ConnectionState.CONNECTED)
                updateConnectedDevicesList()
                
                Result.success(connection)
            } catch (e: Exception) {
                updateConnectionState(getDeviceAddress(device), ConnectionState.ERROR)
                Result.failure(e)
            }
        }
    }
    
    override suspend fun disconnect(device: BluetoothDevice) {
        connectionMutex.withLock {
            val deviceAddress = getDeviceAddress(device)
            
            updateConnectionState(deviceAddress, ConnectionState.DISCONNECTING)
            
            // 移除连接
            gattConnections.remove(deviceAddress)
            updateConnectionState(deviceAddress, ConnectionState.DISCONNECTED)
            updateConnectedDevicesList()
        }
    }
    
    override fun getConnectionState(device: BluetoothDevice): ConnectionState {
        return connectionStates[getDeviceAddress(device)] ?: ConnectionState.DISCONNECTED
    }
    
    override fun getConnectedDevices(): Flow<List<BluetoothDevice>> {
        return connectedDevices
    }
    
    override fun getGattConnection(device: BluetoothDevice): GattConnection? {
        return gattConnections[getDeviceAddress(device)]
    }
    
    /**
     * 更新设备连接状态
     */
    private fun updateConnectionState(deviceAddress: String, state: ConnectionState) {
        connectionStates[deviceAddress] = state
    }
    
    /**
     * 更新已连接设备列表
     */
    private fun updateConnectedDevicesList() {
        connectionScope.launch {
            val connected = gattConnections.values
                .filter { it.connectionState == ConnectionState.CONNECTED }
                .map { it.device }
            _connectedDevices.value = connected
        }
    }
    
    /**
     * 获取设备地址
     * 使用反射访问 AndroidX BluetoothDevice 的内部 fwkDevice 字段来获取地址
     */
    private fun getDeviceAddress(device: BluetoothDevice): String {
        return try {
            // 使用反射访问内部的 fwkDevice 字段
            val fwkDeviceField = device.javaClass.getDeclaredField("fwkDevice")
            fwkDeviceField.isAccessible = true
            val fwkDevice = fwkDeviceField.get(device) as android.bluetooth.BluetoothDevice
            fwkDevice.address
        } catch (e: Exception) {
            // 如果反射失败，使用设备的 toString() 方法尝试提取地址
            val deviceString = device.toString()
            // 尝试从字符串中提取 MAC 地址格式 (XX:XX:XX:XX:XX:XX)
            val macRegex = "([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})".toRegex()
            macRegex.find(deviceString)?.value ?: "Unknown"
        }
    }
}