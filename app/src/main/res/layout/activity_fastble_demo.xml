<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    tools:context=".fastble_demo.FastBleDemoActivity">

    <!-- 标题 -->
    <com.google.android.material.textview.MaterialTextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="FastBLE 功能演示"
        android:textAlignment="center"
        android:textSize="24sp"
        android:textStyle="bold"
        android:layout_marginBottom="16dp" />

    <!-- 状态显示 -->
    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="4dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/tv_status"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="等待初始化..."
                android:textSize="16sp"
                android:textColor="@color/gray_700" />

            <!-- 本地设备蓝牙版本信息 -->
            <LinearLayout
                android:id="@+id/layout_local_bluetooth_info"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:orientation="vertical"
                android:visibility="gone">

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/gray_200"
                    android:layout_marginBottom="8dp" />

                <com.google.android.material.textview.MaterialTextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="本地设备蓝牙信息"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:textColor="@color/blue_600"
                    android:layout_marginBottom="4dp" />

                <com.google.android.material.textview.MaterialTextView
                    android:id="@+id/tv_local_bluetooth_version"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textSize="12sp"
                    android:textColor="@color/gray_600"
                    tools:text="蓝牙 5.0 / BLE 5.0" />

                <com.google.android.material.textview.MaterialTextView
                    android:id="@+id/tv_local_supported_features"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="2dp"
                    android:textSize="11sp"
                    android:textColor="@color/gray_500"
                    android:maxLines="3"
                    android:ellipsize="end"
                    tools:text="支持: BLE, 经典蓝牙, 多广告, 扩展扫描" />

            </LinearLayout>

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- 操作按钮 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="16dp">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_scan"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginEnd="8dp"
            android:text="开始扫描"
            app:icon="@android:drawable/ic_menu_search" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_stop_scan"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="8dp"
            android:text="停止扫描"
            android:enabled="false"
            style="@style/Widget.Material3.Button.OutlinedButton" />

    </LinearLayout>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_disconnect_all"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:text="断开所有连接"
        style="@style/Widget.Material3.Button.OutlinedButton"
        app:icon="@android:drawable/ic_menu_close_clear_cancel" />

    <!-- 设备列表标题 -->
    <com.google.android.material.textview.MaterialTextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="发现的设备"
        android:textSize="18sp"
        android:textStyle="bold"
        android:layout_marginBottom="8dp" />

    <!-- 设备列表 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_devices"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        tools:listitem="@layout/item_ble_device" />

</LinearLayout>