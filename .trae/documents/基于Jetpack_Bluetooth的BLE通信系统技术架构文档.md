# 基于Jetpack Bluetooth的BLE通信系统技术架构文档

## 1. 架构设计

```mermaid
graph TD
    A[Android应用层] --> B[BLE管理层]
    B --> C[AndroidX Bluetooth SDK]
    C --> D[Android蓝牙框架]
    D --> E[蓝牙硬件层]

    subgraph "应用层组件"
        F[扫描Activity]
        G[连接Activity]
        H[数据传输Activity]
        I[设备管理Activity]
    end

    subgraph "BLE管理层"
        J[BleScanner]
        K[BleConnectionManager]
        L[BleDataTransfer]
        M[BleDeviceRepository]
    end

    subgraph "AndroidX Bluetooth"
        N[BluetoothLe]
        O[GattClientScope]
        P[ScanResult Flow]
        Q[Characteristic Operations]
    end

    A --> F
    A --> G
    A --> H
    A --> I
    
    B --> J
    B --> K
    B --> L
    B --> M
    
    C --> N
    C --> O
    C --> P
    C --> Q
```

## 2. 技术描述

* **前端**: Ko<PERSON>in + Android Jetpack + Material Design 3

* **BLE通信**: AndroidX Bluetooth (androidx.bluetooth:bluetooth:1.0.0-alpha02)

* **异步处理**: Kotlin Coroutines + Flow

* **依赖注入**: Hilt

* **数据持久化**: Room Database (可选)

## 3. 路由定义

| 路由                        | 目的                     |
| ------------------------- | ---------------------- |
| /scan                     | 设备扫描页面，启动BLE扫描并展示发现的设备 |
| /connect/{deviceAddress}  | 设备连接页面，建立与指定设备的GATT连接  |
| /transfer/{deviceAddress} | 数据传输页面，执行特征值读写和通知订阅    |
| /devices                  | 设备管理页面，管理已连接设备和连接历史    |

## 4. API定义

### 4.1 核心BLE管理接口

**设备扫描接口**

```Kotlin
interface BleScanner {
    fun startScan(
        filters: List<ScanFilter> = emptyList(),
        timeoutMs: Long = 10000
    ): Flow<ScanResult>
    
    suspend fun stopScan()
    fun isScanning(): Boolean
}
```

**连接管理接口**

```Kotlin
interface BleConnectionManager {
    suspend fun connect(
        device: BluetoothDevice,
        autoConnect: Boolean = false
    ): Result<GattConnection>
    
    suspend fun disconnect(device: BluetoothDevice)
    fun getConnectionState(device: BluetoothDevice): ConnectionState
    fun getConnectedDevices(): Flow<List<BluetoothDevice>>
}
```

**数据传输接口**

```Kotlin
interface BleDataTransfer {
    suspend fun readCharacteristic(
        connection: GattConnection,
        serviceUuid: UUID,
        characteristicUuid: UUID
    ): Result<ByteArray>
    
    suspend fun writeCharacteristic(
        connection: GattConnection,
        serviceUuid: UUID,
        characteristicUuid: UUID,
        data: ByteArray
    ): Result<Unit>
    
    fun subscribeToNotifications(
        connection: GattConnection,
        serviceUuid: UUID,
        characteristicUuid: UUID
    ): Flow<ByteArray>
}
```

### 4.2 数据模型定义

**设备信息模型**

```Kotlin
data class BleDeviceInfo(
    val device: BluetoothDevice,
    val name: String?,
    val address: String,
    val rssi: Int,
    val scanRecord: ByteArray?,
    val lastScanTime: Long = System.currentTimeMillis()
)
```

**连接状态模型**

```Kotlin
enum class ConnectionState {
    DISCONNECTED,
    CONNECTING,
    CONNECTED,
    DISCONNECTING,
    ERROR
}
```

**GATT连接封装**

```Kotlin
data class GattConnection(
    val device: BluetoothDevice,
    val services: List<BluetoothGattService>,
    val connectionState: ConnectionState,
    internal val gattScope: GattClientScope
)
```

## 5. 服务架构图

```mermaid
graph TD
    A[UI Layer] --> B[ViewModel Layer]
    B --> C[Repository Layer]
    C --> D[Data Source Layer]
    
    subgraph "UI Layer"
        E[ScanFragment]
        F[ConnectionFragment]
        G[TransferFragment]
        H[DeviceListFragment]
    end
    
    subgraph "ViewModel Layer"
        I[ScanViewModel]
        J[ConnectionViewModel]
        K[TransferViewModel]
        L[DeviceViewModel]
    end
    
    subgraph "Repository Layer"
        M[BleRepository]
        N[DeviceRepository]
    end
    
    subgraph "Data Source Layer"
        O[AndroidX Bluetooth]
        P[Local Database]
        Q[Preferences]
    end
```

## 6. 数据模型

### 6.1 数据模型定义

```mermaid
erDiagram
    DEVICE ||--o{ CONNECTION_HISTORY : has
    DEVICE ||--o{ GATT_SERVICE : contains
    GATT_SERVICE ||--o{ CHARACTERISTIC : includes
    CHARACTERISTIC ||--o{ NOTIFICATION_DATA : generates

    DEVICE {
        string address PK
        string name
        int rssi
        blob scan_record
        timestamp last_seen
    }
    
    CONNECTION_HISTORY {
        int id PK
        string device_address FK
        timestamp connected_at
        timestamp disconnected_at
        string disconnect_reason
    }
    
    GATT_SERVICE {
        string uuid PK
        string device_address FK
        string service_type
        boolean is_primary
    }
    
    CHARACTERISTIC {
        string uuid PK
        string service_uuid FK
        int properties
        blob last_value
        timestamp last_updated
    }
    
    NOTIFICATION_DATA {
        int id PK
        string characteristic_uuid FK
        blob data
        timestamp received_at
    }
```

### 6.2 数据定义语言

**设备表 (devices)**

```sql
-- 创建设备表
CREATE TABLE devices (
    address TEXT PRIMARY KEY,
    name TEXT,
    rssi INTEGER,
    scan_record BLOB,
    last_seen INTEGER NOT NULL,
    created_at INTEGER DEFAULT (strftime('%s', 'now'))
);

-- 创建索引
CREATE INDEX idx_devices_last_seen ON devices(last_seen DESC);
CREATE INDEX idx_devices_name ON devices(name);

-- 初始化数据
INSERT INTO devices (address, name, rssi, last_seen) VALUES
('00:11:22:33:44:55', 'Hi-XH Device 1', -45, strftime('%s', 'now')),
('00:11:22:33:44:56', 'Hi-XH Device 2', -52, strftime('%s', 'now'));
```

**连接历史表 (connection\_history)**

```sql
-- 创建连接历史表
CREATE TABLE connection_history (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    device_address TEXT NOT NULL,
    connected_at INTEGER NOT NULL,
    disconnected_at INTEGER,
    disconnect_reason TEXT,
    FOREIGN KEY (device_address) REFERENCES devices(address)
);

-- 创建索引
CREATE INDEX idx_connection_history_device ON connection_history(device_address);
CREATE INDEX idx_connection_history_time ON connection_history(connected_at DESC);
```

**GATT服务表 (gatt\_services)**

```sql
-- 创建GATT服务表
CREATE TABLE gatt_services (
    uuid TEXT,
    device_address TEXT,
    service_type TEXT,
    is_primary INTEGER DEFAULT 1,
    discovered_at INTEGER DEFAULT (strftime('%s', 'now')),
    PRIMARY KEY (uuid, device_address),
    FOREIGN KEY (device_address) REFERENCES devices(address)
);
```

**特征值表 (characteristics)**

```sql
-- 创建特征值表
CREATE TABLE characteristics (
    uuid TEXT,
    service_uuid TEXT,
    device_address TEXT,
    properties INTEGER,
    last_value BLOB,
    last_updated INTEGER,
    PRIMARY KEY (uuid, service_uuid, device_address),
    FOREIGN KEY (service_uuid, device_address) REFERENCES gatt_services(uuid, device_address)
);
```

**通知数据表 (notification\_data)**

```sql
-- 创建通知数据表
CREATE TABLE notification_data (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    characteristic_uuid TEXT NOT NULL,
    service_uuid TEXT NOT NULL,
    device_address TEXT NOT NULL,
    data BLOB NOT NULL,
    received_at INTEGER DEFAULT (strftime('%s', 'now')),
    FOREIGN KEY (characteristic_uuid, service_uuid, device_address) 
        REFERENCES characteristics(uuid, service_uuid, device_address)
);

-- 创建索引
CREATE INDEX idx_notification_data_time ON notification_data(received_at DESC);
CREATE INDEX idx_notification_data_char ON notification_data(characteristic_uuid, service_uuid, device_address);
```

