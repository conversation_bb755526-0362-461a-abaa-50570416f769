# 基于Jetpack Bluetooth的BLE通信系统产品需求文档

## 1. 产品概述

本项目旨在构建一个基于AndroidX Bluetooth (Jetpack Bluetooth) 的现代化BLE通信系统，完全替换现有的FastBLE实现。系统将利用Kotlin协程、Flow等现代Android开发技术，提供高效、稳定的蓝牙低功耗设备通信能力。

该系统主要解决Android应用与BLE设备之间的通信问题，为开发者提供简洁易用的API接口，支持设备发现、连接管理、数据传输等核心功能。目标是打造一个符合现代Android开发规范的BLE通信框架。

## 2. 核心功能

### 2.1 用户角色

本系统主要面向开发者使用，无需区分不同用户角色。

### 2.2 功能模块

基于Jetpack Bluetooth的BLE通信系统包含以下核心页面：

1. **设备扫描页面**：BLE设备发现与过滤、扫描状态管理、设备列表展示
2. **设备连接页面**：连接状态管理、服务发现、连接参数配置
3. **数据传输页面**：特征值读写、通知订阅、数据格式转换
4. **设备管理页面**：已连接设备列表、连接历史、设备信息展示

### 2.3 页面详情

| 页面名称 | 模块名称 | 功能描述 |
|---------|---------|----------|
| 设备扫描页面 | 扫描控制器 | 使用BluetoothLe.scan()启动BLE扫描，通过Flow<ScanResult>接收扫描结果，支持设备名称和服务UUID过滤 |
| 设备扫描页面 | 设备列表 | 展示扫描到的BLE设备信息，包括设备名称、MAC地址、RSSI信号强度，支持实时更新 |
| 设备连接页面 | 连接管理器 | 使用BluetoothLe.connectGatt()建立GATT连接，通过GattClientScope管理连接生命周期 |
| 设备连接页面 | 服务发现 | 在GattClientScope中自动发现设备服务和特征值，构建服务树结构 |
| 数据传输页面 | 读写操作 | 通过GattClientScope.readCharacteristic()和writeCharacteristic()实现数据读写 |
| 数据传输页面 | 通知管理 | 使用GattClientScope.subscribeToNotifications()订阅特征值通知，通过Flow接收数据 |
| 设备管理页面 | 连接状态 | 实时监控设备连接状态，支持主动断开连接和重连机制 |
| 设备管理页面 | 设备信息 | 展示设备详细信息，包括支持的服务、特征值属性、连接参数等 |

## 3. 核心流程

### 主要用户操作流程

**设备扫描流程**：用户启动扫描 → 系统调用BluetoothLe.scan() → 通过Flow接收ScanResult → 过滤并展示设备列表 → 用户选择目标设备

**设备连接流程**：用户点击连接 → 系统调用BluetoothLe.connectGatt() → 进入GattClientScope → 自动发现服务 → 连接成功回调

**数据传输流程**：连接建立后 → 选择目标特征值 → 执行读写操作或订阅通知 → 通过协程和Flow处理数据 → 更新UI显示

```mermaid
graph TD
    A[设备扫描页面] --> B[设备连接页面]
    B --> C[数据传输页面]
    C --> D[设备管理页面]
    D --> B
    A --> D
```

## 4. 用户界面设计

### 4.1 设计风格

- **主色调**：Material Design 3 主题色彩，主色为#1976D2（蓝色），辅助色为#FFC107（琥珀色）
- **按钮样式**：Material Design 3 圆角按钮，支持涟漪效果
- **字体**：Roboto字体，标题使用16sp，正文使用14sp，辅助文本使用12sp
- **布局风格**：卡片式布局，使用RecyclerView展示列表，支持下拉刷新
- **图标风格**：Material Design Icons，蓝牙相关图标使用蓝色主题

### 4.2 页面设计概览

| 页面名称 | 模块名称 | UI元素 |
|---------|---------|--------|
| 设备扫描页面 | 扫描控制 | FloatingActionButton扫描按钮，使用蓝牙图标，支持扫描状态动画效果 |
| 设备扫描页面 | 设备列表 | RecyclerView卡片布局，每个设备卡片包含设备名称、MAC地址、RSSI信号强度指示器 |
| 设备连接页面 | 连接状态 | 顶部状态栏显示连接进度，使用CircularProgressIndicator和状态文本 |
| 设备连接页面 | 服务列表 | ExpandableListView展示服务和特征值层级结构，支持展开折叠 |
| 数据传输页面 | 操作面板 | 底部操作栏包含读取、写入、订阅按钮，使用Material Design按钮样式 |
| 数据传输页面 | 数据显示 | 中央数据展示区域，支持十六进制和ASCII格式切换显示 |
| 设备管理页面 | 设备卡片 | 网格布局展示已连接设备，每个卡片显示设备基本信息和连接状态 |

### 4.3 响应式设计

系统采用移动端优先设计，支持手机和平板设备。在平板设备上采用双栏布局，左侧显示设备列表，右侧显示详细信息。支持触摸手势操作，包括滑动刷新、长按菜单等交互方式。